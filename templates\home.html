{% extends "base.html" %}

{% block content %}
<!-- <PERSON> Banner -->
<section class="hero-banner bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="display-4 fw-bold"><PERSON><PERSON><PERSON> thị điện thoại và laptop</h1>
                <p class="lead"><PERSON><PERSON><PERSON> h<PERSON>, g<PERSON><PERSON> tố<PERSON>, b<PERSON>o hành uy tín</p>
                <a href="/api/v1/products" class="btn btn-light btn-lg">
                    Xem sản phẩm <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="col-md-6 text-center">
                <img src="/static/images/hero-banner.png" alt="Hero Banner" class="img-fluid" style="max-height: 300px;">
            </div>
        </div>
    </div>
</section>

<!-- Categories -->
<section class="categories py-5">
    <div class="container">
        <h2 class="text-center mb-5"><PERSON><PERSON> m<PERSON> sản phẩm</h2>
        <div class="row">
            {% for category in categories %}
            <div class="col-md-2 col-6 mb-4">
                <a href="/api/v1/category/{{ category.slug }}" class="text-decoration-none">
                    <div class="category-card text-center p-3 border rounded hover-shadow">
                        <div class="category-icon mb-3">
                            {% if category.icon_url %}
                                <img src="{{ category.icon_url }}" alt="{{ category.name }}" class="img-fluid" style="height: 50px;">
                            {% else %}
                                <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                            {% endif %}
                        </div>
                        <h6 class="category-name">{{ category.name }}</h6>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="featured-products py-5 bg-light">
    <div class="container">
        <div class="row align-items-center mb-4">
            <div class="col">
                <h2>Sản phẩm nổi bật</h2>
            </div>
            <div class="col-auto">
                <a href="/api/v1/products?is_featured=true" class="btn btn-outline-primary">
                    Xem tất cả <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
        
        <div class="row">
            {% for product in featured_products %}
            <div class="col-lg-3 col-md-4 col-6 mb-4">
                <div class="product-card card h-100 border-0 shadow-sm">
                    <div class="product-image position-relative">
                        <a href="/api/v1/product/{{ product.slug }}">
                            {% if product.main_image_url %}
                                <img src="{{ product.main_image_url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                            {% else %}
                                <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </a>
                        
                        {% if product.discount_percentage > 0 %}
                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                            -{{ product.discount_percentage|round(0) }}%
                        </span>
                        {% endif %}
                        
                        <div class="product-actions position-absolute top-0 end-0 m-2">
                            <button class="btn btn-sm btn-outline-light rounded-circle" onclick="addToWishlist({{ product.id }})">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">
                            <a href="/api/v1/product/{{ product.slug }}" class="text-decoration-none text-dark">
                                {{ product.name }}
                            </a>
                        </h6>
                        
                        {% if product.short_description %}
                        <p class="card-text text-muted small">{{ product.short_description }}</p>
                        {% endif %}
                        
                        <div class="price-section mt-auto">
                            <div class="current-price h5 text-danger mb-1">
                                {{ "{:,.0f}".format(product.price) }}₫
                            </div>
                            
                            {% if product.original_price and product.original_price > product.price %}
                            <div class="original-price text-muted text-decoration-line-through small">
                                {{ "{:,.0f}".format(product.original_price) }}₫
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="product-rating mb-2">
                            <div class="stars text-warning">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <small class="text-muted">(128 đánh giá)</small>
                        </div>
                        
                        <button class="btn btn-primary btn-sm" onclick="addToCart({{ product.id }})">
                            <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Brands -->
<section class="brands py-5">
    <div class="container">
        <h2 class="text-center mb-5">Thương hiệu nổi bật</h2>
        <div class="row">
            {% for brand in brands %}
            <div class="col-lg-3 col-md-4 col-6 mb-4">
                <a href="/api/v1/products?brand_id={{ brand.id }}" class="text-decoration-none">
                    <div class="brand-card text-center p-4 border rounded hover-shadow">
                        {% if brand.logo_url %}
                            <img src="{{ brand.logo_url }}" alt="{{ brand.name }}" class="img-fluid mb-3" style="height: 60px;">
                        {% else %}
                            <div class="brand-placeholder bg-light rounded p-3 mb-3">
                                <h5 class="mb-0">{{ brand.name }}</h5>
                            </div>
                        {% endif %}
                        <h6 class="brand-name">{{ brand.name }}</h6>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Promotions -->
<section class="promotions py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">Khuyến mãi hot</h2>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="promo-card bg-gradient-primary text-white p-4 rounded">
                    <h4>Giảm 20%</h4>
                    <p>Cho đơn hàng từ 5 triệu</p>
                    <small>Áp dụng đến 31/12/2024</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="promo-card bg-gradient-success text-white p-4 rounded">
                    <h4>Miễn phí vận chuyển</h4>
                    <p>Cho đơn hàng từ 500k</p>
                    <small>Toàn quốc</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="promo-card bg-gradient-warning text-white p-4 rounded">
                    <h4>Trả góp 0%</h4>
                    <p>Lãi suất 0% trong 12 tháng</p>
                    <small>Áp dụng thẻ tín dụng</small>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function addToCart(productId) {
    // TODO: Implement add to cart functionality
    alert('Thêm vào giỏ hàng: ' + productId);
}

function addToWishlist(productId) {
    // TODO: Implement wishlist functionality
    alert('Thêm vào yêu thích: ' + productId);
}
</script>
{% endblock %}
