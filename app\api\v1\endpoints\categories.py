from typing import List
from fastapi import APIRouter, HTTPException
from app.schemas.category import Category, CategoryCreate, CategoryUpdate
from app.models.category import FAKE_CATEGORIES

router = APIRouter()


@router.get("/", response_model=List[Category])
async def get_categories():
    """
    L<PERSON>y danh sách tất cả danh mục
    """
    return FAKE_CATEGORIES


@router.get("/{category_id}", response_model=Category)
async def get_category(category_id: int):
    """
    Lấy thông tin chi tiết danh mục
    """
    for category in FAKE_CATEGORIES:
        if category["id"] == category_id:
            return category
    raise HTTPException(status_code=404, detail="Category not found")


@router.get("/slug/{slug}", response_model=Category)
async def get_category_by_slug(slug: str):
    """
    Lấy danh mục theo slug
    """
    for category in FAKE_CATEGORIES:
        if category["slug"] == slug:
            return category
    raise HTTPException(status_code=404, detail="Category not found")


@router.post("/", response_model=Category)
async def create_category(category: CategoryCreate):
    """
    Tạo danh mục mới
    """
    new_id = max([cat["id"] for cat in FAKE_CATEGORIES]) + 1 if FAKE_CATEGORIES else 1
    new_category = {
        "id": new_id,
        **category.dict()
    }
    FAKE_CATEGORIES.append(new_category)
    return new_category


@router.put("/{category_id}", response_model=Category)
async def update_category(category_id: int, category: CategoryUpdate):
    """
    Cập nhật thông tin danh mục
    """
    for i, existing_category in enumerate(FAKE_CATEGORIES):
        if existing_category["id"] == category_id:
            updated_category = existing_category.copy()
            update_data = category.dict(exclude_unset=True)
            updated_category.update(update_data)
            FAKE_CATEGORIES[i] = updated_category
            return updated_category
    raise HTTPException(status_code=404, detail="Category not found")


@router.delete("/{category_id}")
async def delete_category(category_id: int):
    """
    Xóa danh mục
    """
    for i, category in enumerate(FAKE_CATEGORIES):
        if category["id"] == category_id:
            deleted_category = FAKE_CATEGORIES.pop(i)
            return {"message": f"Category {deleted_category['name']} deleted successfully"}
    raise HTTPException(status_code=404, detail="Category not found")
