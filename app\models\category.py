# Category model for e-commerce
"""
Ví dụ với SQLAlchemy:

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.sql import func
from app.db.base_class import Base

class Category(Base):
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    slug = Column(String(255), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(500), nullable=True)
    icon_url = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    products = relationship("Product", back_populates="category")
"""

# Fake data cho development
FAKE_CATEGORIES = [
    {
        "id": 1,
        "name": "Điện thoại",
        "slug": "dien-thoai",
        "description": "Điện thoại thông minh các loại",
        "image_url": "/static/images/categories/phone.jpg",
        "icon_url": "/static/images/icons/phone.svg",
        "is_active": True,
        "sort_order": 1
    },
    {
        "id": 2,
        "name": "Laptop",
        "slug": "laptop",
        "description": "Máy tính xách tay, laptop gaming, văn phòng",
        "image_url": "/static/images/categories/laptop.jpg",
        "icon_url": "/static/images/icons/laptop.svg",
        "is_active": True,
        "sort_order": 2
    },
    {
        "id": 3,
        "name": "Phụ kiện",
        "slug": "phu-kien",
        "description": "Phụ kiện điện thoại, laptop, tai nghe",
        "image_url": "/static/images/categories/accessories.jpg",
        "icon_url": "/static/images/icons/accessories.svg",
        "is_active": True,
        "sort_order": 3
    },
    {
        "id": 4,
        "name": "Smartwatch",
        "slug": "smartwatch",
        "description": "Đồng hồ thông minh",
        "image_url": "/static/images/categories/smartwatch.jpg",
        "icon_url": "/static/images/icons/smartwatch.svg",
        "is_active": True,
        "sort_order": 4
    },
    {
        "id": 5,
        "name": "Tablet",
        "slug": "tablet",
        "description": "Máy tính bảng iPad, Android",
        "image_url": "/static/images/categories/tablet.jpg",
        "icon_url": "/static/images/icons/tablet.svg",
        "is_active": True,
        "sort_order": 5
    }
]
