// Main JavaScript for TheGioiDiDong Clone

// Global variables
let cart = {
    items: [],
    total: 0
};

let user = null;
let authToken = localStorage.getItem('authToken');

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    checkAuthStatus();
    loadCartSummary();
    setupEventListeners();
}

// Check authentication status
function checkAuthStatus() {
    if (authToken) {
        // Validate token and get user info
        fetch('/api/v1/users/me', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                // Token invalid, remove it
                localStorage.removeItem('authToken');
                authToken = null;
                throw new Error('Invalid token');
            }
        })
        .then(userData => {
            user = userData;
            updateUserUI();
        })
        .catch(error => {
            console.log('Not authenticated');
        });
    }
}

// Update UI based on authentication status
function updateUserUI() {
    const loginBtn = document.getElementById('loginBtn');
    if (user && loginBtn) {
        loginBtn.innerHTML = `<i class="fas fa-user"></i> ${user.full_name || user.email}`;
        loginBtn.href = '#';
        loginBtn.onclick = showUserMenu;
    }
}

// Load cart summary
function loadCartSummary() {
    if (authToken) {
        fetch('/api/v1/cart/summary', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        })
        .then(response => response.json())
        .then(data => {
            updateCartUI(data);
        })
        .catch(error => {
            console.log('Error loading cart:', error);
        });
    }
}

// Update cart UI
function updateCartUI(cartData) {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.textContent = cartData.total_items || 0;
    }
}

// Setup event listeners
function setupEventListeners() {
    // Login button
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn && !user) {
        loginBtn.addEventListener('click', showLoginModal);
    }
    
    // Cart button
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', showCart);
    }
    
    // Search form
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
    }
}

// Show login modal
function showLoginModal(e) {
    e.preventDefault();
    
    // Create login modal HTML
    const modalHTML = `
        <div class="modal fade" id="loginModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Đăng nhập</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Mật khẩu</label>
                                <input type="password" class="form-control" id="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Đăng nhập</button>
                        </form>
                        <hr>
                        <p class="text-center">
                            Chưa có tài khoản? 
                            <a href="#" onclick="showRegisterModal()">Đăng ký ngay</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
    
    // Handle form submission
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Remove modal when hidden
    document.getElementById('loginModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Handle login
function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
    })
    .then(response => response.json())
    .then(data => {
        if (data.access_token) {
            authToken = data.access_token;
            localStorage.setItem('authToken', authToken);
            user = data.user;
            
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            
            // Update UI
            updateUserUI();
            loadCartSummary();
            
            showToast('Đăng nhập thành công!', 'success');
        } else {
            showToast('Đăng nhập thất bại!', 'error');
        }
    })
    .catch(error => {
        console.error('Login error:', error);
        showToast('Có lỗi xảy ra!', 'error');
    });
}

// Show user menu
function showUserMenu(e) {
    e.preventDefault();
    
    const menuHTML = `
        <div class="dropdown-menu show position-absolute" style="right: 0; top: 100%;">
            <a class="dropdown-item" href="#" onclick="showProfile()">
                <i class="fas fa-user"></i> Thông tin cá nhân
            </a>
            <a class="dropdown-item" href="#" onclick="showOrders()">
                <i class="fas fa-shopping-bag"></i> Đơn hàng của tôi
            </a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Đăng xuất
            </a>
        </div>
    `;
    
    // Remove existing menu
    const existingMenu = document.querySelector('.dropdown-menu.show');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }
    
    // Add menu
    e.target.closest('a').insertAdjacentHTML('afterend', menuHTML);
    
    // Remove menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function removeMenu() {
            const menu = document.querySelector('.dropdown-menu.show');
            if (menu) {
                menu.remove();
            }
            document.removeEventListener('click', removeMenu);
        });
    }, 100);
}

// Logout
function logout() {
    localStorage.removeItem('authToken');
    authToken = null;
    user = null;
    
    // Reset UI
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
        loginBtn.innerHTML = '<i class="fas fa-user"></i> Đăng nhập';
        loginBtn.onclick = showLoginModal;
    }
    
    // Reset cart
    updateCartUI({ total_items: 0 });
    
    showToast('Đăng xuất thành công!', 'success');
}

// Show cart
function showCart(e) {
    e.preventDefault();
    
    if (!authToken) {
        showLoginModal(e);
        return;
    }
    
    // TODO: Implement cart modal/page
    window.location.href = '/api/v1/cart';
}

// Add to cart function
function addToCart(productId, quantity = 1) {
    if (!authToken) {
        showToast('Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng!', 'warning');
        return;
    }
    
    fetch('/api/v1/cart/items', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            showToast('Đã thêm sản phẩm vào giỏ hàng!', 'success');
            loadCartSummary();
        } else {
            showToast('Có lỗi xảy ra!', 'error');
        }
    })
    .catch(error => {
        console.error('Add to cart error:', error);
        showToast('Có lỗi xảy ra!', 'error');
    });
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastHTML = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Add toast
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    // Show toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // Remove toast after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// Handle search
function handleSearch(e) {
    const query = e.target.querySelector('input[name="q"]').value.trim();
    if (!query) {
        e.preventDefault();
        showToast('Vui lòng nhập từ khóa tìm kiếm!', 'warning');
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(amount);
}

// Export functions for global use
window.addToCart = addToCart;
window.showLoginModal = showLoginModal;
window.showUserMenu = showUserMenu;
window.logout = logout;
