# Product model for e-commerce
"""
Ví dụ với SQLAlchemy:

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(500), index=True, nullable=False)
    slug = Column(String(500), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    short_description = Column(Text, nullable=True)
    sku = Column(String(100), unique=True, index=True, nullable=False)
    
    # Pricing
    price = Column(Float, nullable=False)
    original_price = Column(Float, nullable=True)
    discount_percentage = Column(Float, default=0)
    
    # Stock
    stock_quantity = Column(Integer, default=0)
    is_in_stock = Column(Boolean, default=True)
    
    # Images
    main_image_url = Column(String(500), nullable=True)
    image_urls = Column(JSON, nullable=True)  # List of image URLs
    
    # Specifications
    specifications = Column(JSON, nullable=True)  # Product specs as JSON
    
    # SEO
    meta_title = Column(String(255), nullable=True)
    meta_description = Column(Text, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Foreign Keys
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=False)
    brand_id = Column(Integer, ForeignKey("brands.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    category = relationship("Category", back_populates="products")
    brand = relationship("Brand", back_populates="products")
    reviews = relationship("Review", back_populates="product")
    cart_items = relationship("CartItem", back_populates="product")
    order_items = relationship("OrderItem", back_populates="product")
"""

# Fake data cho development
FAKE_PRODUCTS = [
    {
        "id": 1,
        "name": "iPhone 16 Pro Max 256GB",
        "slug": "iphone-16-pro-max-256gb",
        "description": "iPhone 16 Pro Max với chip A18 Pro mạnh mẽ, camera 48MP, màn hình Super Retina XDR 6.9 inch",
        "short_description": "iPhone 16 Pro Max - Đỉnh cao công nghệ",
        "sku": "IP16PM256",
        "price": 34990000,
        "original_price": 36990000,
        "discount_percentage": 5.4,
        "stock_quantity": 50,
        "is_in_stock": True,
        "main_image_url": "/static/images/products/iphone-16-pro-max.jpg",
        "image_urls": [
            "/static/images/products/iphone-16-pro-max-1.jpg",
            "/static/images/products/iphone-16-pro-max-2.jpg",
            "/static/images/products/iphone-16-pro-max-3.jpg"
        ],
        "specifications": {
            "screen": "6.9 inch Super Retina XDR",
            "chip": "A18 Pro",
            "camera": "48MP + 12MP + 12MP",
            "battery": "4422 mAh",
            "storage": "256GB",
            "ram": "8GB",
            "os": "iOS 18"
        },
        "category_id": 1,
        "brand_id": 1,
        "is_active": True,
        "is_featured": True
    },
    {
        "id": 2,
        "name": "Samsung Galaxy S25 Ultra 512GB",
        "slug": "samsung-galaxy-s25-ultra-512gb",
        "description": "Galaxy S25 Ultra với S Pen tích hợp, camera 200MP, màn hình Dynamic AMOLED 2X 6.8 inch",
        "short_description": "Galaxy S25 Ultra - Sức mạnh vượt trội",
        "sku": "SGS25U512",
        "price": 32990000,
        "original_price": 35990000,
        "discount_percentage": 8.3,
        "stock_quantity": 30,
        "is_in_stock": True,
        "main_image_url": "/static/images/products/galaxy-s25-ultra.jpg",
        "image_urls": [
            "/static/images/products/galaxy-s25-ultra-1.jpg",
            "/static/images/products/galaxy-s25-ultra-2.jpg"
        ],
        "specifications": {
            "screen": "6.8 inch Dynamic AMOLED 2X",
            "chip": "Snapdragon 8 Gen 4",
            "camera": "200MP + 50MP + 12MP + 10MP",
            "battery": "5000 mAh",
            "storage": "512GB",
            "ram": "12GB",
            "os": "Android 15"
        },
        "category_id": 1,
        "brand_id": 2,
        "is_active": True,
        "is_featured": True
    },
    {
        "id": 3,
        "name": "MacBook Pro M4 14 inch 512GB",
        "slug": "macbook-pro-m4-14-inch-512gb",
        "description": "MacBook Pro M4 với chip M4 mạnh mẽ, màn hình Liquid Retina XDR 14.2 inch",
        "short_description": "MacBook Pro M4 - Hiệu năng đỉnh cao",
        "sku": "MBPM414512",
        "price": 52990000,
        "original_price": 54990000,
        "discount_percentage": 3.6,
        "stock_quantity": 20,
        "is_in_stock": True,
        "main_image_url": "/static/images/products/macbook-pro-m4.jpg",
        "image_urls": [
            "/static/images/products/macbook-pro-m4-1.jpg",
            "/static/images/products/macbook-pro-m4-2.jpg"
        ],
        "specifications": {
            "screen": "14.2 inch Liquid Retina XDR",
            "chip": "Apple M4",
            "storage": "512GB SSD",
            "ram": "18GB",
            "battery": "70Wh",
            "weight": "1.55kg",
            "os": "macOS Sequoia"
        },
        "category_id": 2,
        "brand_id": 1,
        "is_active": True,
        "is_featured": True
    }
]
