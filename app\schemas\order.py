from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from enum import Enum
from .product import ProductList
from .user import Address


class OrderStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PROCESSING = "processing"
    SHIPPING = "shipping"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"
    RETURNED = "returned"


class PaymentMethod(str, Enum):
    COD = "cod"  # Cash on delivery
    BANK_TRANSFER = "bank_transfer"
    CREDIT_CARD = "credit_card"
    E_WALLET = "e_wallet"


class PaymentStatus(str, Enum):
    PENDING = "pending"
    PAID = "paid"
    FAILED = "failed"
    REFUNDED = "refunded"


class OrderItemBase(BaseModel):
    product_id: int
    quantity: int
    price: float  # Price at time of order


class OrderItem(OrderItemBase):
    id: int
    order_id: int
    product: Optional[ProductList] = None
    subtotal: float

    class Config:
        from_attributes = True


class OrderBase(BaseModel):
    shipping_address: Address
    payment_method: PaymentMethod
    notes: Optional[str] = None


class OrderCreate(BaseModel):
    shipping_address_id: int
    payment_method: PaymentMethod
    notes: Optional[str] = None


class Order(BaseModel):
    id: int
    order_number: str
    user_id: int
    status: OrderStatus
    payment_status: PaymentStatus
    payment_method: PaymentMethod
    
    # Amounts
    subtotal: float
    shipping_fee: float
    tax_amount: float
    discount_amount: float
    total_amount: float
    
    # Address
    shipping_address: Address
    
    # Items
    items: List[OrderItem] = []
    
    # Notes
    notes: Optional[str] = None
    
    # Timestamps
    created_at: datetime
    updated_at: Optional[datetime] = None
    shipped_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class OrderList(BaseModel):
    """Simplified order for listing"""
    id: int
    order_number: str
    status: OrderStatus
    payment_status: PaymentStatus
    total_amount: float
    created_at: datetime

    class Config:
        from_attributes = True
