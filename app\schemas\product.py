from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from .category import Category
from .brand import Brand


class ProductBase(BaseModel):
    name: str
    slug: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    sku: str
    price: float
    original_price: Optional[float] = None
    discount_percentage: float = 0
    stock_quantity: int = 0
    is_in_stock: bool = True
    main_image_url: Optional[str] = None
    image_urls: Optional[List[str]] = None
    specifications: Optional[Dict[str, Any]] = None
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    is_active: bool = True
    is_featured: bool = False
    category_id: int
    brand_id: int


class ProductCreate(ProductBase):
    pass


class ProductUpdate(BaseModel):
    name: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    sku: Optional[str] = None
    price: Optional[float] = None
    original_price: Optional[float] = None
    discount_percentage: Optional[float] = None
    stock_quantity: Optional[int] = None
    is_in_stock: Optional[bool] = None
    main_image_url: Optional[str] = None
    image_urls: Optional[List[str]] = None
    specifications: Optional[Dict[str, Any]] = None
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    category_id: Optional[int] = None
    brand_id: Optional[int] = None


class Product(ProductBase):
    id: int
    category: Optional[Category] = None
    brand: Optional[Brand] = None

    class Config:
        from_attributes = True


class ProductList(BaseModel):
    """Schema for product list with minimal info"""
    id: int
    name: str
    slug: str
    short_description: Optional[str] = None
    price: float
    original_price: Optional[float] = None
    discount_percentage: float = 0
    main_image_url: Optional[str] = None
    is_in_stock: bool = True
    category_id: int
    brand_id: int

    class Config:
        from_attributes = True


class ProductSearch(BaseModel):
    """Schema for product search parameters"""
    q: Optional[str] = None  # Search query
    category_id: Optional[int] = None
    brand_id: Optional[int] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    is_featured: Optional[bool] = None
    is_in_stock: Optional[bool] = None
    sort_by: Optional[str] = "created_at"  # price_asc, price_desc, name, created_at
    page: int = 1
    limit: int = 20
