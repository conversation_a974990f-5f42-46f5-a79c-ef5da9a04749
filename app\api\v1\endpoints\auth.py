from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from app.schemas.user import Use<PERSON><PERSON><PERSON>, User<PERSON>ogin, User
from app.core.security import create_access_token, verify_password, get_password_hash

router = APIRouter()
security = HTTPBearer()

# Fake users database
FAKE_USERS = [
    {
        "id": 1,
        "email": "<EMAIL>",
        "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
        "full_name": "Administrator",
        "phone": "0123456789",
        "is_active": True
    }
]


@router.post("/register", response_model=User)
async def register(user: UserCreate):
    """
    Đăng ký tài khoản mới
    """
    # Check if email already exists
    for existing_user in FAKE_USERS:
        if existing_user["email"] == user.email:
            raise HTTPException(status_code=400, detail="Em<PERSON> already registered")
    
    # Create new user
    new_id = max([u["id"] for u in FAKE_USERS]) + 1 if FAKE_USERS else 1
    hashed_password = get_password_hash(user.password)
    
    new_user = {
        "id": new_id,
        "email": user.email,
        "hashed_password": hashed_password,
        "full_name": user.full_name,
        "phone": user.phone,
        "is_active": True
    }
    
    FAKE_USERS.append(new_user)
    
    # Return user without password
    return {
        "id": new_user["id"],
        "email": new_user["email"],
        "full_name": new_user["full_name"],
        "phone": new_user["phone"],
        "is_active": new_user["is_active"]
    }


@router.post("/login")
async def login(user_credentials: UserLogin):
    """
    Đăng nhập
    """
    # Find user by email
    user = None
    for u in FAKE_USERS:
        if u["email"] == user_credentials.email:
            user = u
            break
    
    if not user:
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Verify password
    if not verify_password(user_credentials.password, user["hashed_password"]):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Create access token
    access_token = create_access_token(data={"sub": user["email"]})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user["id"],
            "email": user["email"],
            "full_name": user["full_name"],
            "phone": user["phone"],
            "is_active": user["is_active"]
        }
    }


@router.post("/logout")
async def logout():
    """
    Đăng xuất (client sẽ xóa token)
    """
    return {"message": "Successfully logged out"}
