from typing import List
from fastapi import APIRouter, HTTPException, Depends, Query
from app.schemas.review import Review, ReviewCreate, ProductReviewSummary
from app.api.deps import get_current_user
from app.models.product import FAKE_PRODUCTS

router = APIRouter()

# Fake reviews database
FAKE_REVIEWS = []


@router.get("/product/{product_id}", response_model=List[Review])
async def get_product_reviews(
    product_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=50)
):
    """
    <PERSON><PERSON>y đ<PERSON>h giá của sản phẩm
    """
    product_reviews = [r for r in FAKE_REVIEWS if r["product_id"] == product_id]
    
    start = (page - 1) * limit
    end = start + limit
    
    return product_reviews[start:end]


@router.get("/product/{product_id}/summary", response_model=ProductReviewSummary)
async def get_product_review_summary(product_id: int):
    """
    <PERSON><PERSON><PERSON> tó<PERSON> tắt đ<PERSON>h giá sản phẩm
    """
    product_reviews = [r for r in FAKE_REVIEWS if r["product_id"] == product_id]
    
    if not product_reviews:
        return {
            "product_id": product_id,
            "total_reviews": 0,
            "average_rating": 0,
            "rating_distribution": {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        }
    
    total_reviews = len(product_reviews)
    total_rating = sum(r["rating"] for r in product_reviews)
    average_rating = total_rating / total_reviews
    
    # Calculate rating distribution
    rating_distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
    for review in product_reviews:
        rating_distribution[review["rating"]] += 1
    
    return {
        "product_id": product_id,
        "total_reviews": total_reviews,
        "average_rating": round(average_rating, 1),
        "rating_distribution": rating_distribution
    }


@router.post("/", response_model=Review)
async def create_review(
    review: ReviewCreate,
    current_user_email: str = Depends(get_current_user)
):
    """
    Tạo đánh giá sản phẩm
    """
    # Check if product exists
    product_exists = any(p["id"] == review.product_id for p in FAKE_PRODUCTS)
    if not product_exists:
        raise HTTPException(status_code=404, detail="Product not found")
    
    # Check if user already reviewed this product
    existing_review = any(
        r["product_id"] == review.product_id and r["user_email"] == current_user_email
        for r in FAKE_REVIEWS
    )
    if existing_review:
        raise HTTPException(status_code=400, detail="You have already reviewed this product")
    
    new_review = {
        "id": len(FAKE_REVIEWS) + 1,
        "product_id": review.product_id,
        "user_email": current_user_email,
        "user_name": "Anonymous User",  # Would get from user data
        "rating": review.rating,
        "title": review.title,
        "comment": review.comment,
        "is_verified_purchase": False,  # Would check if user bought this product
        "created_at": "2024-01-01T00:00:00"
    }
    
    FAKE_REVIEWS.append(new_review)
    return new_review


@router.get("/my-reviews", response_model=List[Review])
async def get_my_reviews(current_user_email: str = Depends(get_current_user)):
    """
    Lấy đánh giá của user hiện tại
    """
    return [r for r in FAKE_REVIEWS if r["user_email"] == current_user_email]
