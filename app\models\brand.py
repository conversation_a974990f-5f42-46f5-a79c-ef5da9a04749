# Brand model for e-commerce
"""
Ví dụ với SQLAlchemy:

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.sql import func
from app.db.base_class import Base

class Brand(Base):
    __tablename__ = "brands"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    slug = Column(String(255), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    logo_url = Column(String(500), nullable=True)
    website = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    products = relationship("Product", back_populates="brand")
"""

# Fake data cho development
FAKE_BRANDS = [
    {
        "id": 1,
        "name": "Apple",
        "slug": "apple",
        "description": "Công ty công nghệ hàng đầu thế giới",
        "logo_url": "/static/images/brands/apple.png",
        "website": "https://www.apple.com",
        "is_active": True,
        "sort_order": 1
    },
    {
        "id": 2,
        "name": "Samsung",
        "slug": "samsung",
        "description": "Tập đoàn công nghệ Hàn Quốc",
        "logo_url": "/static/images/brands/samsung.png",
        "website": "https://www.samsung.com",
        "is_active": True,
        "sort_order": 2
    },
    {
        "id": 3,
        "name": "Xiaomi",
        "slug": "xiaomi",
        "description": "Thương hiệu công nghệ Trung Quốc",
        "logo_url": "/static/images/brands/xiaomi.png",
        "website": "https://www.mi.com",
        "is_active": True,
        "sort_order": 3
    },
    {
        "id": 4,
        "name": "OPPO",
        "slug": "oppo",
        "description": "Thương hiệu điện thoại thông minh",
        "logo_url": "/static/images/brands/oppo.png",
        "website": "https://www.oppo.com",
        "is_active": True,
        "sort_order": 4
    },
    {
        "id": 5,
        "name": "Vivo",
        "slug": "vivo",
        "description": "Thương hiệu smartphone cao cấp",
        "logo_url": "/static/images/brands/vivo.png",
        "website": "https://www.vivo.com",
        "is_active": True,
        "sort_order": 5
    },
    {
        "id": 6,
        "name": "HP",
        "slug": "hp",
        "description": "Thương hiệu laptop và máy tính",
        "logo_url": "/static/images/brands/hp.png",
        "website": "https://www.hp.com",
        "is_active": True,
        "sort_order": 6
    },
    {
        "id": 7,
        "name": "Dell",
        "slug": "dell",
        "description": "Thương hiệu máy tính hàng đầu",
        "logo_url": "/static/images/brands/dell.png",
        "website": "https://www.dell.com",
        "is_active": True,
        "sort_order": 7
    },
    {
        "id": 8,
        "name": "Asus",
        "slug": "asus",
        "description": "Thương hiệu laptop gaming",
        "logo_url": "/static/images/brands/asus.png",
        "website": "https://www.asus.com",
        "is_active": True,
        "sort_order": 8
    }
]
