from typing import List
from fastapi import APIRouter, HTTPException, Depends
from app.schemas.item import Item, ItemCreate, ItemUpdate
from app.services.item_service import ItemService

router = APIRouter()


# Fake database từ code cũ
fake_items_db = [
    {"id": 1, "item_name": "Foo", "description": "A very nice Item"},
    {"id": 2, "item_name": "Bar", "description": "The bartenders"},
    {"id": 3, "item_name": "<PERSON><PERSON>", "description": "There goes my hero"}
]


@router.get("/", response_model=List[Item])
async def list_items(skip: int = 0, limit: int = 10):
    """
    Lấy danh sách items với phân trang
    """
    return fake_items_db[skip: skip + limit]


@router.get("/{item_id}", response_model=Item)
async def get_item(item_id: int):
    """
    <PERSON><PERSON><PERSON> thông tin chi tiết của một item
    """
    for item in fake_items_db:
        if item["id"] == item_id:
            return item
    raise HTTPException(status_code=404, detail="Item not found")


@router.post("/", response_model=Item)
async def create_item(item: ItemCreate):
    """
    Tạo item mới
    """
    new_id = max([item["id"] for item in fake_items_db]) + 1 if fake_items_db else 1
    new_item = {
        "id": new_id,
        "item_name": item.item_name,
        "description": item.description
    }
    fake_items_db.append(new_item)
    return new_item


@router.put("/{item_id}", response_model=Item)
async def update_item(item_id: int, item: ItemUpdate):
    """
    Cập nhật thông tin item
    """
    for i, existing_item in enumerate(fake_items_db):
        if existing_item["id"] == item_id:
            updated_item = existing_item.copy()
            if item.item_name is not None:
                updated_item["item_name"] = item.item_name
            if item.description is not None:
                updated_item["description"] = item.description
            fake_items_db[i] = updated_item
            return updated_item
    raise HTTPException(status_code=404, detail="Item not found")


@router.delete("/{item_id}")
async def delete_item(item_id: int):
    """
    Xóa item
    """
    for i, item in enumerate(fake_items_db):
        if item["id"] == item_id:
            deleted_item = fake_items_db.pop(i)
            return {"message": f"Item {deleted_item['item_name']} deleted successfully"}
    raise HTTPException(status_code=404, detail="Item not found")
