from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from .product import ProductList


class CartItemBase(BaseModel):
    product_id: int
    quantity: int = 1


class CartItemCreate(CartItemBase):
    pass


class CartItemUpdate(BaseModel):
    quantity: int


class CartItem(CartItemBase):
    id: int
    cart_id: int
    product: Optional[ProductList] = None
    subtotal: float
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class CartBase(BaseModel):
    user_id: int


class Cart(CartBase):
    id: int
    items: List[CartItem] = []
    total_items: int = 0
    total_amount: float = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class CartSummary(BaseModel):
    """Summary of cart for quick display"""
    total_items: int
    total_amount: float
