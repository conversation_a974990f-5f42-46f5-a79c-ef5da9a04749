{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <!-- Search Header -->
    <div class="search-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                {% if query %}
                <h1 class="h3 mb-2">Kế<PERSON> quả tìm kiếm cho "{{ query }}"</h1>
                <p class="text-muted">Tì<PERSON> thấy {{ total_results }} sản phẩm</p>
                {% else %}
                <h1 class="h3 mb-2">Tìm kiếm sản phẩm</h1>
                <p class="text-muted">Nhập từ khóa để tìm kiếm sản phẩm</p>
                {% endif %}
            </div>
            <div class="col-md-4">
                <!-- Advanced Search Form -->
                <form class="search-form" action="/api/v1/search" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" 
                               placeholder="T<PERSON><PERSON> kiếm sản phẩm..." value="{{ query or '' }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {% if query %}
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-md-3">
            <div class="filters-sidebar">
                <h5 class="mb-3">Lọc kết quả</h5>
                
                <div class="filter-section mb-4">
                    <h6 class="filter-title">Danh mục</h6>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-phone" value="1">
                            <label class="form-check-label" for="cat-phone">Điện thoại</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-laptop" value="2">
                            <label class="form-check-label" for="cat-laptop">Laptop</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-accessories" value="3">
                            <label class="form-check-label" for="cat-accessories">Phụ kiện</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-smartwatch" value="4">
                            <label class="form-check-label" for="cat-smartwatch">Smartwatch</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="cat-tablet" value="5">
                            <label class="form-check-label" for="cat-tablet">Tablet</label>
                        </div>
                    </div>
                </div>

                <div class="filter-section mb-4">
                    <h6 class="filter-title">Thương hiệu</h6>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-apple" value="1">
                            <label class="form-check-label" for="brand-apple">Apple</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-samsung" value="2">
                            <label class="form-check-label" for="brand-samsung">Samsung</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-xiaomi" value="3">
                            <label class="form-check-label" for="brand-xiaomi">Xiaomi</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-oppo" value="4">
                            <label class="form-check-label" for="brand-oppo">OPPO</label>
                        </div>
                    </div>
                </div>

                <div class="filter-section mb-4">
                    <h6 class="filter-title">Khoảng giá</h6>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-under-5m" value="0-5000000">
                            <label class="form-check-label" for="price-under-5m">Dưới 5 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-5m-10m" value="5000000-10000000">
                            <label class="form-check-label" for="price-5m-10m">5 - 10 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-10m-20m" value="10000000-20000000">
                            <label class="form-check-label" for="price-10m-20m">10 - 20 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-over-20m" value="20000000-999999999">
                            <label class="form-check-label" for="price-over-20m">Trên 20 triệu</label>
                        </div>
                    </div>
                </div>

                <button class="btn btn-primary w-100" onclick="applySearchFilters()">
                    <i class="fas fa-filter"></i> Áp dụng bộ lọc
                </button>
                
                <button class="btn btn-outline-secondary w-100 mt-2" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </button>
            </div>
        </div>

        <!-- Search Results -->
        <div class="col-md-9">
            <!-- Sort Options -->
            <div class="sort-options mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <span class="text-muted">Sắp xếp theo:</span>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="sortSelect" onchange="sortSearchResults()">
                            <option value="relevance">Liên quan nhất</option>
                            <option value="price_asc">Giá thấp đến cao</option>
                            <option value="price_desc">Giá cao đến thấp</option>
                            <option value="name">Tên A-Z</option>
                            <option value="newest">Mới nhất</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Search Results Grid -->
            {% if products %}
            <div class="search-results" id="searchResults">
                <div class="row">
                    {% for product in products %}
                    <div class="col-lg-4 col-md-6 col-sm-6 mb-4 product-item" 
                         data-category="{{ product.category_id }}"
                         data-brand="{{ product.brand_id }}" 
                         data-price="{{ product.price }}"
                         data-name="{{ product.name|lower }}">
                        <div class="product-card card h-100 border-0 shadow-sm">
                            <div class="product-image position-relative">
                                <a href="/api/v1/product/{{ product.slug }}">
                                    {% if product.main_image_url %}
                                        <img src="{{ product.main_image_url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                                    {% else %}
                                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}
                                </a>
                                
                                {% if product.discount_percentage > 0 %}
                                <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                    -{{ product.discount_percentage|round(0) }}%
                                </span>
                                {% endif %}
                                
                                <div class="product-actions position-absolute top-0 end-0 m-2">
                                    <button class="btn btn-sm btn-outline-light rounded-circle" onclick="addToWishlist({{ product.id }})">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">
                                    <a href="/api/v1/product/{{ product.slug }}" class="text-decoration-none text-dark">
                                        {{ product.name }}
                                    </a>
                                </h6>
                                
                                {% if product.short_description %}
                                <p class="card-text text-muted small">{{ product.short_description }}</p>
                                {% endif %}
                                
                                <div class="price-section mt-auto">
                                    <div class="current-price h5 text-danger mb-1">
                                        {{ "{:,.0f}".format(product.price) }}₫
                                    </div>
                                    
                                    {% if product.original_price and product.original_price > product.price %}
                                    <div class="original-price text-muted text-decoration-line-through small">
                                        {{ "{:,.0f}".format(product.original_price) }}₫
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="product-rating mb-2">
                                    <div class="stars text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <small class="text-muted">({{ (product.id * 17) % 150 + 30 }} đánh giá)</small>
                                </div>
                                
                                <button class="btn btn-primary btn-sm" onclick="addToCart({{ product.id }})">
                                    <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <!-- No Results -->
            <div class="no-results text-center py-5">
                <i class="fas fa-search fa-5x text-muted mb-3"></i>
                <h4>Không tìm thấy sản phẩm nào</h4>
                <p class="text-muted">Thử tìm kiếm với từ khóa khác hoặc kiểm tra chính tả</p>
                
                <!-- Search Suggestions -->
                <div class="search-suggestions mt-4">
                    <h6>Gợi ý tìm kiếm:</h6>
                    <div class="d-flex flex-wrap justify-content-center gap-2">
                        <a href="/api/v1/search?q=iphone" class="btn btn-outline-primary btn-sm">iPhone</a>
                        <a href="/api/v1/search?q=samsung" class="btn btn-outline-primary btn-sm">Samsung</a>
                        <a href="/api/v1/search?q=laptop" class="btn btn-outline-primary btn-sm">Laptop</a>
                        <a href="/api/v1/search?q=macbook" class="btn btn-outline-primary btn-sm">MacBook</a>
                        <a href="/api/v1/search?q=airpods" class="btn btn-outline-primary btn-sm">AirPods</a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
    <!-- Search Suggestions for Empty Query -->
    <div class="search-suggestions">
        <h4 class="mb-4">Tìm kiếm phổ biến</h4>
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="/api/v1/search?q=iphone" class="btn btn-outline-primary w-100">
                    <i class="fas fa-mobile-alt"></i> iPhone
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/api/v1/search?q=samsung" class="btn btn-outline-primary w-100">
                    <i class="fas fa-mobile-alt"></i> Samsung
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/api/v1/search?q=laptop" class="btn btn-outline-primary w-100">
                    <i class="fas fa-laptop"></i> Laptop
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/api/v1/search?q=macbook" class="btn btn-outline-primary w-100">
                    <i class="fas fa-laptop"></i> MacBook
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function applySearchFilters() {
    const selectedCategories = [];
    document.querySelectorAll('input[type="checkbox"][id^="cat-"]:checked').forEach(checkbox => {
        selectedCategories.push(checkbox.value);
    });
    
    const selectedBrands = [];
    document.querySelectorAll('input[type="checkbox"][id^="brand-"]:checked').forEach(checkbox => {
        selectedBrands.push(checkbox.value);
    });
    
    const priceRange = document.querySelector('input[name="priceRange"]:checked');
    
    // Filter products
    const productItems = document.querySelectorAll('.product-item');
    productItems.forEach(item => {
        let show = true;
        
        // Category filter
        if (selectedCategories.length > 0 && !selectedCategories.includes(item.dataset.category)) {
            show = false;
        }
        
        // Brand filter
        if (selectedBrands.length > 0 && !selectedBrands.includes(item.dataset.brand)) {
            show = false;
        }
        
        // Price filter
        if (priceRange) {
            const [minPrice, maxPrice] = priceRange.value.split('-').map(Number);
            const productPrice = parseFloat(item.dataset.price);
            if (productPrice < minPrice || productPrice > maxPrice) {
                show = false;
            }
        }
        
        item.style.display = show ? 'block' : 'none';
    });
}

function clearFilters() {
    // Clear all checkboxes and radio buttons
    document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
        input.checked = false;
    });
    
    // Show all products
    document.querySelectorAll('.product-item').forEach(item => {
        item.style.display = 'block';
    });
}

function sortSearchResults() {
    const sortValue = document.getElementById('sortSelect').value;
    const resultsGrid = document.querySelector('.search-results .row');
    if (!resultsGrid) return;
    
    const products = Array.from(resultsGrid.children);
    
    products.sort((a, b) => {
        switch (sortValue) {
            case 'price_asc':
                return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
            case 'price_desc':
                return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
            case 'name':
                return a.dataset.name.localeCompare(b.dataset.name);
            case 'newest':
                // Sort by product ID (assuming higher ID = newer)
                return parseInt(b.querySelector('.btn[onclick*="addToCart"]').getAttribute('onclick').match(/\d+/)[0]) - 
                       parseInt(a.querySelector('.btn[onclick*="addToCart"]').getAttribute('onclick').match(/\d+/)[0]);
            default: // relevance
                return 0;
        }
    });
    
    // Re-append sorted products
    products.forEach(product => resultsGrid.appendChild(product));
}

function addToWishlist(productId) {
    showToast('Đã thêm vào danh sách yêu thích!', 'success');
}
</script>
{% endblock %}
