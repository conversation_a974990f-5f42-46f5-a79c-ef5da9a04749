from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class ReviewBase(BaseModel):
    product_id: int
    rating: int  # 1-5 stars
    title: Optional[str] = None
    comment: Optional[str] = None


class ReviewCreate(ReviewBase):
    pass


class ReviewUpdate(BaseModel):
    rating: Optional[int] = None
    title: Optional[str] = None
    comment: Optional[str] = None


class Review(ReviewBase):
    id: int
    user_id: int
    user_name: Optional[str] = None  # For display
    is_verified_purchase: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProductReviewSummary(BaseModel):
    """Summary of reviews for a product"""
    product_id: int
    total_reviews: int
    average_rating: float
    rating_distribution: dict  # {1: count, 2: count, ...}
