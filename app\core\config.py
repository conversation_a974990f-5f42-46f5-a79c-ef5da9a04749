from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # App Info
    PROJECT_NAME: str = "TheGioiDiDong Clone"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "E-commerce platform inspired by TheGioiDiDong"
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = "your-super-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "http://localhost:3001"
    ]
    
    # Database (for future use)
    DATABASE_URL: Optional[str] = None
    
    # File Upload
    UPLOAD_DIR: str = "static/uploads"
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 5MB
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/webp"]
    
    # Pagination
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # Cache
    REDIS_URL: Optional[str] = None
    CACHE_EXPIRE_SECONDS: int = 3600
    
    # Email (for future use)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: Optional[int] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # Payment (for future use)
    VNPAY_TMN_CODE: Optional[str] = None
    VNPAY_HASH_SECRET: Optional[str] = None
    MOMO_PARTNER_CODE: Optional[str] = None
    MOMO_ACCESS_KEY: Optional[str] = None
    
    # Shipping
    DEFAULT_SHIPPING_FEE: float = 30000  # 30k VND
    FREE_SHIPPING_THRESHOLD: float = 500000  # 500k VND
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
