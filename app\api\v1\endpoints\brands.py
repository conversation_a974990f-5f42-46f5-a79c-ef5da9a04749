from typing import List
from fastapi import APIRouter, HTTPException
from app.schemas.brand import Brand, BrandCreate, BrandUpdate
from app.models.brand import FAKE_BRANDS

router = APIRouter()


@router.get("/", response_model=List[Brand])
async def get_brands():
    """
    <PERSON><PERSON><PERSON> danh sách tất cả thương hiệu
    """
    return FAKE_BRANDS


@router.get("/{brand_id}", response_model=Brand)
async def get_brand(brand_id: int):
    """
    Lấy thông tin chi tiết thương hiệu
    """
    for brand in FAKE_BRANDS:
        if brand["id"] == brand_id:
            return brand
    raise HTTPException(status_code=404, detail="Brand not found")


@router.get("/slug/{slug}", response_model=Brand)
async def get_brand_by_slug(slug: str):
    """
    Lấy thương hiệu theo slug
    """
    for brand in FAKE_BRANDS:
        if brand["slug"] == slug:
            return brand
    raise HTTPException(status_code=404, detail="Brand not found")


@router.post("/", response_model=Brand)
async def create_brand(brand: BrandCreate):
    """
    Tạo thương hiệu mới
    """
    new_id = max([b["id"] for b in FAKE_BRANDS]) + 1 if FAKE_BRANDS else 1
    new_brand = {
        "id": new_id,
        **brand.dict()
    }
    FAKE_BRANDS.append(new_brand)
    return new_brand


@router.put("/{brand_id}", response_model=Brand)
async def update_brand(brand_id: int, brand: BrandUpdate):
    """
    Cập nhật thông tin thương hiệu
    """
    for i, existing_brand in enumerate(FAKE_BRANDS):
        if existing_brand["id"] == brand_id:
            updated_brand = existing_brand.copy()
            update_data = brand.dict(exclude_unset=True)
            updated_brand.update(update_data)
            FAKE_BRANDS[i] = updated_brand
            return updated_brand
    raise HTTPException(status_code=404, detail="Brand not found")


@router.delete("/{brand_id}")
async def delete_brand(brand_id: int):
    """
    Xóa thương hiệu
    """
    for i, brand in enumerate(FAKE_BRANDS):
        if brand["id"] == brand_id:
            deleted_brand = FAKE_BRANDS.pop(i)
            return {"message": f"Brand {deleted_brand['name']} deleted successfully"}
    raise HTTPException(status_code=404, detail="Brand not found")
