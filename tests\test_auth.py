def test_register_user(client, sample_user):
    """
    Test user registration
    """
    response = client.post("/api/v1/auth/register", json=sample_user)
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == sample_user["email"]
    assert data["full_name"] == sample_user["full_name"]
    assert "id" in data
    # Password should not be returned
    assert "password" not in data
    assert "hashed_password" not in data


def test_register_duplicate_email(client, sample_user):
    """
    Test registration with duplicate email
    """
    # Register first user
    client.post("/api/v1/auth/register", json=sample_user)
    
    # Try to register again with same email
    response = client.post("/api/v1/auth/register", json=sample_user)
    assert response.status_code == 400
    data = response.json()
    assert "already registered" in data["detail"].lower()


def test_login_user(client, sample_user):
    """
    Test user login
    """
    # Register user first
    client.post("/api/v1/auth/register", json=sample_user)
    
    # Login
    login_data = {
        "email": sample_user["email"],
        "password": sample_user["password"]
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "token_type" in data
    assert data["token_type"] == "bearer"
    assert "user" in data


def test_login_invalid_credentials(client):
    """
    Test login with invalid credentials
    """
    login_data = {
        "email": "<EMAIL>",
        "password": "wrongpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 401
    data = response.json()
    assert "invalid" in data["detail"].lower()


def test_login_wrong_password(client, sample_user):
    """
    Test login with wrong password
    """
    # Register user first
    client.post("/api/v1/auth/register", json=sample_user)
    
    # Login with wrong password
    login_data = {
        "email": sample_user["email"],
        "password": "wrongpassword"
    }
    response = client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 401


def test_logout(client):
    """
    Test user logout
    """
    response = client.post("/api/v1/auth/logout")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
