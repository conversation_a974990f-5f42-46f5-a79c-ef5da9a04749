# FastAPI Configuration
PROJECT_NAME=FastAPI Project
VERSION=1.0.0
DESCRIPTION=A FastAPI project with standard structure
API_V1_STR=/api/v1

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database (khi có database)
# DATABASE_URL=sqlite:///./app.db
# DATABASE_URL=postgresql://user:password@localhost/dbname
# DATABASE_URL=mysql://user:password@localhost/dbname

# CORS Origins
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Environment
ENVIRONMENT=development
