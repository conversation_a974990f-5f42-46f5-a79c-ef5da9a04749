# FastAPI E-commerce Configuration

# App Info
PROJECT_NAME=TheGioiDiDong Clone
VERSION=1.0.0
DESCRIPTION=E-commerce platform inspired by TheGioiDiDong
API_V1_STR=/api/v1

# Security
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS Origins (comma separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8000,http://127.0.0.1:8000

# Database (when implemented)
# DATABASE_URL=sqlite:///./ecommerce.db
# DATABASE_URL=postgresql://user:password@localhost/ecommerce
# DATABASE_URL=mysql://user:password@localhost/ecommerce

# File Upload
UPLOAD_DIR=static/uploads
MAX_FILE_SIZE=5242880
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Cache (Redis)
# REDIS_URL=redis://localhost:6379
CACHE_EXPIRE_SECONDS=3600

# Email (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Payment Gateways
# VNPay
# VNPAY_TMN_CODE=your-vnpay-tmn-code
# VNPAY_HASH_SECRET=your-vnpay-hash-secret

# MoMo
# MOMO_PARTNER_CODE=your-momo-partner-code
# MOMO_ACCESS_KEY=your-momo-access-key

# Shipping
DEFAULT_SHIPPING_FEE=30000
FREE_SHIPPING_THRESHOLD=500000

# Environment
ENVIRONMENT=development
