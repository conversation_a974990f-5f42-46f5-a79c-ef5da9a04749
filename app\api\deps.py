from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>
from jose import jwt, JWTError

from app.core.config import settings
from app.core.security import ALGORITHM

security = HTTPBearer()


def get_current_user(token: str = Depends(security)):
    """
    Dependency để lấy thông tin user hiện tại từ JWT token
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token.credentials, settings.SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return username


# Dependency cho database session (sẽ implement sau khi có database)
def get_db() -> Generator:
    """
    Database dependency - sẽ được implement khi có database
    """
    pass
