from typing import Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from app.core.security import verify_token

security = HTTPBearer()


def get_current_user(token: str = Depends(security)) -> str:
    """
    Dependency để lấy thông tin user hiện tại từ JWT token
    """
    return verify_token(token.credentials)


# Dependency cho database session (sẽ implement sau khi có database)
def get_db() -> Generator:
    """
    Database dependency - sẽ được implement khi có database
    """
    pass
