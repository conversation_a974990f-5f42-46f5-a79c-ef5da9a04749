{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
            <li class="breadcrumb-item active">{{ category.name }}</li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="category-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">{{ category.name }}</h1>
                {% if category.description %}
                <p class="text-muted">{{ category.description }}</p>
                {% endif %}
                <p class="text-muted">Tìm thấy {{ products|length }} sản phẩm</p>
            </div>
            <div class="col-md-4 text-end">
                {% if category.image_url %}
                <img src="{{ category.image_url }}" alt="{{ category.name }}" class="img-fluid" style="max-height: 100px;">
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-md-3">
            <div class="filters-sidebar">
                <div class="filter-section mb-4">
                    <h5 class="filter-title">Thương hiệu</h5>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-apple" value="1">
                            <label class="form-check-label" for="brand-apple">Apple</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-samsung" value="2">
                            <label class="form-check-label" for="brand-samsung">Samsung</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-xiaomi" value="3">
                            <label class="form-check-label" for="brand-xiaomi">Xiaomi</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="brand-oppo" value="4">
                            <label class="form-check-label" for="brand-oppo">OPPO</label>
                        </div>
                    </div>
                </div>

                <div class="filter-section mb-4">
                    <h5 class="filter-title">Khoảng giá</h5>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-under-5m" value="0-5000000">
                            <label class="form-check-label" for="price-under-5m">Dưới 5 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-5m-10m" value="5000000-10000000">
                            <label class="form-check-label" for="price-5m-10m">5 - 10 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-10m-20m" value="10000000-20000000">
                            <label class="form-check-label" for="price-10m-20m">10 - 20 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-20m-50m" value="20000000-50000000">
                            <label class="form-check-label" for="price-20m-50m">20 - 50 triệu</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="priceRange" id="price-over-50m" value="50000000-999999999">
                            <label class="form-check-label" for="price-over-50m">Trên 50 triệu</label>
                        </div>
                    </div>
                </div>

                <div class="filter-section mb-4">
                    <h5 class="filter-title">Tình trạng</h5>
                    <div class="filter-options">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="in-stock" checked>
                            <label class="form-check-label" for="in-stock">Còn hàng</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="on-sale">
                            <label class="form-check-label" for="on-sale">Đang giảm giá</label>
                        </div>
                    </div>
                </div>

                <button class="btn btn-primary w-100" onclick="applyFilters()">
                    <i class="fas fa-filter"></i> Áp dụng bộ lọc
                </button>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-md-9">
            <!-- Sort Options -->
            <div class="sort-options mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <span class="text-muted">Sắp xếp theo:</span>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="sortSelect" onchange="sortProducts()">
                            <option value="created_at">Mới nhất</option>
                            <option value="price_asc">Giá thấp đến cao</option>
                            <option value="price_desc">Giá cao đến thấp</option>
                            <option value="name">Tên A-Z</option>
                            <option value="popular">Phổ biến</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="productsGrid">
                <div class="row">
                    {% for product in products %}
                    <div class="col-lg-4 col-md-6 col-sm-6 mb-4 product-item" 
                         data-brand="{{ product.brand_id }}" 
                         data-price="{{ product.price }}"
                         data-in-stock="{{ product.is_in_stock|lower }}"
                         data-on-sale="{{ (product.discount_percentage > 0)|lower }}">
                        <div class="product-card card h-100 border-0 shadow-sm">
                            <div class="product-image position-relative">
                                <a href="/api/v1/product/{{ product.slug }}">
                                    {% if product.main_image_url %}
                                        <img src="{{ product.main_image_url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                                    {% else %}
                                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}
                                </a>
                                
                                {% if product.discount_percentage > 0 %}
                                <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                    -{{ product.discount_percentage|round(0) }}%
                                </span>
                                {% endif %}
                                
                                {% if not product.is_in_stock %}
                                <div class="out-of-stock-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50">
                                    <span class="badge bg-danger">Hết hàng</span>
                                </div>
                                {% endif %}
                                
                                <div class="product-actions position-absolute top-0 end-0 m-2">
                                    <button class="btn btn-sm btn-outline-light rounded-circle" onclick="addToWishlist({{ product.id }})">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">
                                    <a href="/api/v1/product/{{ product.slug }}" class="text-decoration-none text-dark">
                                        {{ product.name }}
                                    </a>
                                </h6>
                                
                                {% if product.short_description %}
                                <p class="card-text text-muted small">{{ product.short_description }}</p>
                                {% endif %}
                                
                                <div class="price-section mt-auto">
                                    <div class="current-price h5 text-danger mb-1">
                                        {{ "{:,.0f}".format(product.price) }}₫
                                    </div>
                                    
                                    {% if product.original_price and product.original_price > product.price %}
                                    <div class="original-price text-muted text-decoration-line-through small">
                                        {{ "{:,.0f}".format(product.original_price) }}₫
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="product-rating mb-2">
                                    <div class="stars text-warning">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <small class="text-muted">({{ (product.id * 23) % 200 + 50 }} đánh giá)</small>
                                </div>
                                
                                {% if product.is_in_stock %}
                                <button class="btn btn-primary btn-sm" onclick="addToCart({{ product.id }})">
                                    <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                                </button>
                                {% else %}
                                <button class="btn btn-secondary btn-sm" disabled>
                                    <i class="fas fa-times"></i> Hết hàng
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Pagination -->
            {% if products|length >= 20 %}
            <nav aria-label="Product pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">Trước</a>
                    </li>
                    <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">Sau</a>
                    </li>
                </ul>
            </nav>
            {% endif %}

            <!-- No Products Message -->
            {% if products|length == 0 %}
            <div class="no-products text-center py-5">
                <i class="fas fa-search fa-5x text-muted mb-3"></i>
                <h4>Không tìm thấy sản phẩm</h4>
                <p class="text-muted">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
                <a href="/api/v1/products" class="btn btn-primary">Xem tất cả sản phẩm</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function applyFilters() {
    const selectedBrands = [];
    document.querySelectorAll('input[type="checkbox"][id^="brand-"]:checked').forEach(checkbox => {
        selectedBrands.push(checkbox.value);
    });
    
    const priceRange = document.querySelector('input[name="priceRange"]:checked');
    const inStock = document.getElementById('in-stock').checked;
    const onSale = document.getElementById('on-sale').checked;
    
    // Filter products
    const productItems = document.querySelectorAll('.product-item');
    productItems.forEach(item => {
        let show = true;
        
        // Brand filter
        if (selectedBrands.length > 0 && !selectedBrands.includes(item.dataset.brand)) {
            show = false;
        }
        
        // Price filter
        if (priceRange) {
            const [minPrice, maxPrice] = priceRange.value.split('-').map(Number);
            const productPrice = parseFloat(item.dataset.price);
            if (productPrice < minPrice || productPrice > maxPrice) {
                show = false;
            }
        }
        
        // Stock filter
        if (inStock && item.dataset.inStock === 'false') {
            show = false;
        }
        
        // Sale filter
        if (onSale && item.dataset.onSale === 'false') {
            show = false;
        }
        
        item.style.display = show ? 'block' : 'none';
    });
    
    // Update results count
    const visibleProducts = document.querySelectorAll('.product-item[style="display: block"], .product-item:not([style])').length;
    // Update count display if needed
}

function sortProducts() {
    const sortValue = document.getElementById('sortSelect').value;
    const productsGrid = document.querySelector('.products-grid .row');
    const products = Array.from(productsGrid.children);
    
    products.sort((a, b) => {
        switch (sortValue) {
            case 'price_asc':
                return parseFloat(a.dataset.price) - parseFloat(b.dataset.price);
            case 'price_desc':
                return parseFloat(b.dataset.price) - parseFloat(a.dataset.price);
            case 'name':
                return a.querySelector('.card-title a').textContent.localeCompare(b.querySelector('.card-title a').textContent);
            default:
                return 0;
        }
    });
    
    // Re-append sorted products
    products.forEach(product => productsGrid.appendChild(product));
}

function addToWishlist(productId) {
    // TODO: Implement wishlist functionality
    showToast('Đã thêm vào danh sách yêu thích!', 'success');
}
</script>
{% endblock %}
