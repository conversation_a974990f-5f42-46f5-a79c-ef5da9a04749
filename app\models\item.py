# Item database model
# Sẽ đượ<PERSON> implement khi có database (SQLAlchemy, MongoDB, etc.)

"""
Ví dụ với SQLAlchemy:

from sqlalchemy import Column, Integer, String, Text
from app.db.base_class import Base

class Item(Base):
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    item_name = Column(String(255), index=True)
    description = Column(Text, nullable=True)
"""
