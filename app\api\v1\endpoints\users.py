from typing import List
from fastapi import APIRouter, HTTPException, Depends
from app.schemas.user import User, UserUpdate
from app.api.deps import get_current_user

router = APIRouter()

# Import fake users from auth
from .auth import FAKE_USERS


@router.get("/me", response_model=User)
async def get_current_user_info(current_user_email: str = Depends(get_current_user)):
    """
    Lấy thông tin user hiện tại
    """
    for user in FAKE_USERS:
        if user["email"] == current_user_email:
            return {
                "id": user["id"],
                "email": user["email"],
                "full_name": user["full_name"],
                "phone": user["phone"],
                "is_active": user["is_active"]
            }
    raise HTTPException(status_code=404, detail="User not found")


@router.put("/me", response_model=User)
async def update_current_user(
    user_update: UserUpdate,
    current_user_email: str = Depends(get_current_user)
):
    """
    Cậ<PERSON> nhật thông tin user hiện tại
    """
    for i, user in enumerate(FAKE_USERS):
        if user["email"] == current_user_email:
            update_data = user_update.dict(exclude_unset=True)
            
            # Check if email is being changed and already exists
            if "email" in update_data and update_data["email"] != user["email"]:
                for other_user in FAKE_USERS:
                    if other_user["email"] == update_data["email"]:
                        raise HTTPException(status_code=400, detail="Email already exists")
            
            # Update user
            FAKE_USERS[i].update(update_data)
            updated_user = FAKE_USERS[i]
            
            return {
                "id": updated_user["id"],
                "email": updated_user["email"],
                "full_name": updated_user["full_name"],
                "phone": updated_user["phone"],
                "is_active": updated_user["is_active"]
            }
    
    raise HTTPException(status_code=404, detail="User not found")


@router.get("/{user_id}", response_model=User)
async def get_user(user_id: int):
    """
    Lấy thông tin user theo ID (admin only)
    """
    for user in FAKE_USERS:
        if user["id"] == user_id:
            return {
                "id": user["id"],
                "email": user["email"],
                "full_name": user["full_name"],
                "phone": user["phone"],
                "is_active": user["is_active"]
            }
    raise HTTPException(status_code=404, detail="User not found")
