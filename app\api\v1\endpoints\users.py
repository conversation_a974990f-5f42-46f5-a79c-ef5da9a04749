from typing import List
from fastapi import APIRouter, HTTPException, Depends
from app.schemas.user import User, UserCreate
from app.api.deps import get_current_user

router = APIRouter()

# Fake users database
fake_users_db = [
    {
        "id": 1,
        "username": "truong",
        "email": "<EMAIL>",
        "full_name": "<PERSON><PERSON>",
        "age": 24
    },
    {
        "id": 2,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Administrator",
        "age": 30
    }
]


@router.get("/", response_model=List[User])
async def list_users():
    """
    Lấy danh sách users
    """
    return fake_users_db


@router.get("/me", response_model=User)
async def get_current_user_info(current_user: str = Depends(get_current_user)):
    """
    Lấy thông tin user hiện tại
    """
    for user in fake_users_db:
        if user["username"] == current_user:
            return user
    raise HTTPException(status_code=404, detail="User not found")


@router.get("/{user_id}", response_model=User)
async def get_user(user_id: int):
    """
    Lấy thông tin user theo ID
    """
    for user in fake_users_db:
        if user["id"] == user_id:
            return user
    raise HTTPException(status_code=404, detail="User not found")


@router.post("/", response_model=User)
async def create_user(user: UserCreate):
    """
    Tạo user mới
    """
    new_id = max([user["id"] for user in fake_users_db]) + 1 if fake_users_db else 1
    new_user = {
        "id": new_id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "age": user.age
    }
    fake_users_db.append(new_user)
    return new_user
