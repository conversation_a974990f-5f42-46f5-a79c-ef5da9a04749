def test_read_main(client):
    """
    Test root endpoint
    """
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_api_info(client):
    """
    Test API info endpoint
    """
    response = client.get("/api/v1/")
    assert response.status_code == 200


def test_home_page(client):
    """
    Test home page template
    """
    response = client.get("/api/v1/")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


def test_categories_endpoint(client):
    """
    Test categories endpoint
    """
    response = client.get("/api/v1/categories/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    
    # Check first category structure
    category = data[0]
    assert "id" in category
    assert "name" in category
    assert "slug" in category


def test_brands_endpoint(client):
    """
    Test brands endpoint
    """
    response = client.get("/api/v1/brands/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    
    # Check first brand structure
    brand = data[0]
    assert "id" in brand
    assert "name" in brand
    assert "slug" in brand


def test_products_endpoint(client):
    """
    Test products endpoint
    """
    response = client.get("/api/v1/products/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0
    
    # Check first product structure
    product = data[0]
    assert "id" in product
    assert "name" in product
    assert "slug" in product
    assert "price" in product


def test_product_detail(client):
    """
    Test product detail endpoint
    """
    response = client.get("/api/v1/products/1")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == 1
    assert "category" in data
    assert "brand" in data


def test_product_not_found(client):
    """
    Test product not found
    """
    response = client.get("/api/v1/products/999")
    assert response.status_code == 404


def test_featured_products(client):
    """
    Test featured products endpoint
    """
    response = client.get("/api/v1/products/featured")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_products_filter_by_category(client):
    """
    Test products filtering by category
    """
    response = client.get("/api/v1/products/?category_id=1")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    
    # All products should be from category 1
    for product in data:
        assert product["category_id"] == 1


def test_products_filter_by_brand(client):
    """
    Test products filtering by brand
    """
    response = client.get("/api/v1/products/?brand_id=1")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    
    # All products should be from brand 1
    for product in data:
        assert product["brand_id"] == 1


def test_products_filter_by_price(client):
    """
    Test products filtering by price range
    """
    response = client.get("/api/v1/products/?min_price=10000000&max_price=20000000")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    
    # All products should be in price range
    for product in data:
        assert 10000000 <= product["price"] <= 20000000


def test_products_search(client):
    """
    Test products search
    """
    response = client.get("/api/v1/products/?q=iPhone")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_products_sort(client):
    """
    Test products sorting
    """
    response = client.get("/api/v1/products/?sort_by=price_asc")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    
    # Check if sorted by price ascending
    if len(data) > 1:
        for i in range(len(data) - 1):
            assert data[i]["price"] <= data[i + 1]["price"]
