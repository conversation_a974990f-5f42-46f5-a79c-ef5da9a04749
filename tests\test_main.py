def test_read_main(client):
    """
    Test root endpoint
    """
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Chào mừng đến với FastAPI Project!"}


def test_read_items(client):
    """
    Test items endpoint
    """
    response = client.get("/api/v1/items/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_create_item(client):
    """
    Test create item endpoint
    """
    item_data = {
        "item_name": "Test Item",
        "description": "This is a test item"
    }
    response = client.post("/api/v1/items/", json=item_data)
    assert response.status_code == 200
    assert response.json()["item_name"] == "Test Item"
