# FastAPI Project

Dự án FastAPI với cấu trúc thư mục chuẩn và tổ chức code theo best practices.

## Cấu trúc thư mục

```
├── app/                    # Thư mục chính chứa application code
│   ├── __init__.py
│   ├── main.py            # Entry point của ứng dụng
│   ├── api/               # API routes
│   │   ├── __init__.py
│   │   ├── deps.py        # Dependencies
│   │   └── v1/            # API version 1
│   │       ├── __init__.py
│   │       ├── api.py     # Router tổng
│   │       └── endpoints/ # Các endpoint riêng biệt
│   │           ├── __init__.py
│   │           ├── home.py
│   │           ├── items.py
│   │           └── users.py
│   ├── core/              # Core configuration
│   │   ├── __init__.py
│   │   ├── config.py      # Settings và configuration
│   │   └── security.py    # Security utilities
│   ├── models/            # Database models
│   │   ├── __init__.py
│   │   └── item.py
│   ├── schemas/           # Pydantic schemas
│   │   ├── __init__.py
│   │   ├── item.py
│   │   └── user.py
│   └── services/          # Business logic
│       ├── __init__.py
│       └── item_service.py
├── static/                # Static files (CSS, JS, images)
│   ├── css/
│   ├── js/
│   └── images/
├── templates/             # Jinja2 templates
│   └── home.html
├── tests/                 # Test files
│   ├── __init__.py
│   ├── conftest.py
│   └── test_main.py
├── main.py               # Entry point để chạy ứng dụng
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables mẫu
└── README.md            # Documentation
```

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd FastAPI_Bai01
```

2. Tạo virtual environment:
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

4. Tạo file .env từ .env.example:
```bash
cp .env.example .env
```

5. Chỉnh sửa file .env theo cấu hình của bạn.

## Chạy ứng dụng

### Development mode:
```bash
uvicorn app.main:app --reload
```

Hoặc:
```bash
python main.py
```

### Production mode:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## API Documentation

Sau khi chạy ứng dụng, bạn có thể truy cập:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/api/v1/openapi.json

## API Endpoints

### Home
- `GET /` - Trang chủ
- `POST /` - Post data
- `PUT /` - Update data
- `GET /api/v1/home` - Home page với template

### Items
- `GET /api/v1/items/` - Lấy danh sách items
- `GET /api/v1/items/{item_id}` - Lấy item theo ID
- `POST /api/v1/items/` - Tạo item mới
- `PUT /api/v1/items/{item_id}` - Cập nhật item
- `DELETE /api/v1/items/{item_id}` - Xóa item

### Users
- `GET /api/v1/users/` - Lấy danh sách users
- `GET /api/v1/users/{user_id}` - Lấy user theo ID
- `POST /api/v1/users/` - Tạo user mới
- `GET /api/v1/users/me` - Lấy thông tin user hiện tại

## Testing

Chạy tests:
```bash
pytest
```

Chạy tests với coverage:
```bash
pytest --cov=app
```

## Tính năng

- ✅ Cấu trúc thư mục chuẩn FastAPI
- ✅ API versioning (v1)
- ✅ Pydantic schemas cho validation
- ✅ Service layer cho business logic
- ✅ Configuration management với Pydantic Settings
- ✅ Static files và templates
- ✅ Security utilities (JWT, password hashing)
- ✅ Testing setup với pytest
- ✅ CORS configuration
- ✅ Environment variables
- ✅ API documentation

## Phát triển tiếp

- [ ] Thêm database (SQLAlchemy/MongoDB)
- [ ] Implement authentication/authorization
- [ ] Thêm logging
- [ ] Thêm middleware
- [ ] Docker containerization
- [ ] CI/CD pipeline
- [ ] Database migrations
- [ ] Caching (Redis)
- [ ] Background tasks (Celery)

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request
