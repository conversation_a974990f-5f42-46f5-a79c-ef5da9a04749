# TheGioiDiDong Clone - E-commerce Platform

Một trang web bán hàng điện tử được xây dựng bằng FastAPI, lấy cảm hứng từ TheGioiDiDong.com.

## 🚀 Tính năng chính

### 🛍️ **E-commerce Core**
- ✅ <PERSON><PERSON> mục sản phẩm (<PERSON><PERSON><PERSON><PERSON>o<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> ki<PERSON>, Smartwatch, Tablet)
- ✅ Tìm kiếm và lọc sản phẩm theo gi<PERSON>, thư<PERSON><PERSON> hiệu, danh mục
- ✅ Chi tiết sản phẩm với hình ảnh, thông số kỹ thuật
- ✅ Giỏ hàng và quản lý đơn hàng
- ✅ Hệ thống đánh giá sản phẩm
- ✅ Khuyến mãi và ưu đãi

### 👤 **Quản lý người dùng**
- ✅ Đ<PERSON>ng ký, đăng nhập với JWT
- ✅ Quản lý thông tin cá nhân
- ✅ Lịch sử đơn hàng
- ✅ Địa chỉ giao hàng

### 🎨 **Gia<PERSON> diện**
- ✅ Responsive design với Bootstrap 5
- ✅ <PERSON>hiết kế giống TheGioiDiDong
- ✅ Tối ưu cho mobile và desktop
- ✅ Loading states và animations

### 🔧 **Technical Features**
- ✅ FastAPI với async/await
- ✅ Pydantic schemas cho validation
- ✅ JWT authentication
- ✅ CORS middleware
- ✅ API documentation (Swagger/ReDoc)
- ✅ Structured logging
- ✅ Error handling

## 📁 Cấu trúc dự án

```
├── app/                    # Main application
│   ├── api/               # API routes
│   │   ├── deps.py        # Dependencies
│   │   └── v1/            # API version 1
│   │       ├── api.py     # Main router
│   │       └── endpoints/ # Individual endpoints
│   ├── core/              # Core functionality
│   │   ├── config.py      # Settings
│   │   └── security.py    # Security utilities
│   ├── models/            # Data models (fake data)
│   ├── schemas/           # Pydantic schemas
│   └── services/          # Business logic
├── static/                # Static files
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript
│   └── images/           # Images
├── templates/             # Jinja2 templates
├── tests/                 # Test files
├── main.py               # Application entry point
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## 🛠️ Cài đặt và chạy

### 1. **Clone repository**
```bash
git clone <repository-url>
cd thegioididong-clone
```

### 2. **Tạo virtual environment**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. **Cài đặt dependencies**
```bash
pip install -r requirements.txt
```

### 4. **Cấu hình environment**
```bash
cp .env.example .env
# Chỉnh sửa file .env theo cấu hình của bạn
```

### 5. **Chạy ứng dụng**
```bash
# Development mode
python main.py

# Hoặc với uvicorn
uvicorn app.main:app --reload

# Production mode
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 📖 API Documentation

Sau khi chạy ứng dụng, truy cập:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/api/v1/openapi.json

## 🌐 Endpoints chính

### **Public Endpoints**
- `GET /` - Trang chủ
- `GET /api/v1/` - API info
- `GET /api/v1/categories` - Danh sách danh mục
- `GET /api/v1/brands` - Danh sách thương hiệu
- `GET /api/v1/products` - Danh sách sản phẩm (có filter)
- `GET /api/v1/products/{id}` - Chi tiết sản phẩm
- `GET /api/v1/search` - Tìm kiếm sản phẩm

### **Authentication**
- `POST /api/v1/auth/register` - Đăng ký
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/logout` - Đăng xuất

### **User Endpoints** (Cần authentication)
- `GET /api/v1/users/me` - Thông tin user hiện tại
- `PUT /api/v1/users/me` - Cập nhật thông tin

### **Cart & Orders** (Cần authentication)
- `GET /api/v1/cart` - Xem giỏ hàng
- `POST /api/v1/cart/items` - Thêm vào giỏ hàng
- `PUT /api/v1/cart/items/{id}` - Cập nhật số lượng
- `DELETE /api/v1/cart/items/{id}` - Xóa khỏi giỏ hàng
- `GET /api/v1/orders` - Danh sách đơn hàng
- `POST /api/v1/orders` - Tạo đơn hàng mới

### **Reviews**
- `GET /api/v1/reviews/product/{id}` - Đánh giá sản phẩm
- `POST /api/v1/reviews` - Tạo đánh giá (cần auth)

## 🎨 Frontend Pages

- **Trang chủ**: `/api/v1/` - Hiển thị sản phẩm nổi bật, danh mục
- **Danh mục**: `/api/v1/category/{slug}` - Sản phẩm theo danh mục
- **Chi tiết sản phẩm**: `/api/v1/product/{slug}` - Thông tin chi tiết
- **Tìm kiếm**: `/api/v1/search?q={query}` - Kết quả tìm kiếm
- **404**: Trang lỗi không tìm thấy

## 🧪 Testing

```bash
# Chạy tests
pytest

# Chạy với coverage
pytest --cov=app

# Chạy tests cụ thể
pytest tests/test_products.py
```

## 📦 Dependencies chính

- **FastAPI** - Web framework
- **Uvicorn** - ASGI server
- **Pydantic** - Data validation
- **Jinja2** - Template engine
- **python-jose** - JWT handling
- **passlib** - Password hashing
- **Bootstrap 5** - CSS framework
- **Font Awesome** - Icons

## 🔮 Roadmap

### **Phase 1** (Hoàn thành)
- [x] Cấu trúc dự án chuẩn
- [x] API endpoints cơ bản
- [x] Authentication với JWT
- [x] Giao diện responsive
- [x] Giỏ hàng và đơn hàng

### **Phase 2** (Kế hoạch)
- [ ] Database integration (PostgreSQL/MySQL)
- [ ] File upload cho hình ảnh
- [ ] Email notifications
- [ ] Payment integration (VNPay, MoMo)
- [ ] Admin dashboard
- [ ] Inventory management

### **Phase 3** (Tương lai)
- [ ] Redis caching
- [ ] Search optimization (Elasticsearch)
- [ ] Recommendation system
- [ ] Mobile app (React Native)
- [ ] Microservices architecture
- [ ] Docker deployment

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên hệ

- **Email**: <EMAIL>
- **GitHub**: [Your GitHub Profile]
- **Demo**: [Live Demo URL]

## 🙏 Acknowledgments

- [TheGioiDiDong.com](https://thegioididong.com) - Design inspiration
- [FastAPI](https://fastapi.tiangolo.com/) - Amazing web framework
- [Bootstrap](https://getbootstrap.com/) - CSS framework
- [Font Awesome](https://fontawesome.com/) - Icons
