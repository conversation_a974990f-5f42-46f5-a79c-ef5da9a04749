{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
            <li class="breadcrumb-item"><a href="/api/v1/category/{{ category.slug }}">{{ category.name }}</a></li>
            <li class="breadcrumb-item active">{{ product.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Images -->
        <div class="col-md-6">
            <div class="product-images">
                <div class="main-image mb-3">
                    {% if product.main_image_url %}
                        <img src="{{ product.main_image_url }}" class="img-fluid rounded" alt="{{ product.name }}" id="mainImage">
                    {% else %}
                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light rounded" style="height: 400px;">
                            <i class="fas fa-image fa-5x text-muted"></i>
                        </div>
                    {% endif %}
                </div>
                
                {% if product.image_urls and product.image_urls|length > 1 %}
                <div class="thumbnail-images">
                    <div class="row">
                        {% for image_url in product.image_urls %}
                        <div class="col-3">
                            <img src="{{ image_url }}" class="img-fluid rounded thumbnail-img" 
                                 alt="{{ product.name }}" onclick="changeMainImage('{{ image_url }}')">
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-md-6">
            <div class="product-info">
                <h1 class="product-title h3 mb-3">{{ product.name }}</h1>
                
                <!-- Brand -->
                <div class="brand mb-3">
                    <span class="text-muted">Thương hiệu: </span>
                    <a href="/api/v1/products?brand_id={{ brand.id }}" class="text-primary">{{ brand.name }}</a>
                </div>
                
                <!-- Rating -->
                <div class="rating mb-3">
                    <div class="stars text-warning d-inline-block">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <span class="text-muted ms-2">(4.8/5 - 128 đánh giá)</span>
                </div>
                
                <!-- Price -->
                <div class="price-section mb-4">
                    <div class="current-price h2 text-danger mb-2">
                        {{ "{:,.0f}".format(product.price) }}₫
                    </div>
                    
                    {% if product.original_price and product.original_price > product.price %}
                    <div class="original-price text-muted text-decoration-line-through h5">
                        {{ "{:,.0f}".format(product.original_price) }}₫
                    </div>
                    <div class="discount-badge">
                        <span class="badge bg-danger">Giảm {{ product.discount_percentage|round(0) }}%</span>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Stock Status -->
                <div class="stock-status mb-4">
                    {% if product.is_in_stock %}
                        <span class="badge bg-success">
                            <i class="fas fa-check"></i> Còn hàng
                        </span>
                    {% else %}
                        <span class="badge bg-danger">
                            <i class="fas fa-times"></i> Hết hàng
                        </span>
                    {% endif %}
                </div>
                
                <!-- Short Description -->
                {% if product.short_description %}
                <div class="short-description mb-4">
                    <p class="text-muted">{{ product.short_description }}</p>
                </div>
                {% endif %}
                
                <!-- Quantity and Add to Cart -->
                <div class="purchase-section mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label for="quantity" class="form-label">Số lượng:</label>
                            <div class="input-group">
                                <button class="btn btn-outline-secondary" type="button" onclick="decreaseQuantity()">-</button>
                                <input type="number" class="form-control text-center" id="quantity" value="1" min="1" max="10">
                                <button class="btn btn-outline-secondary" type="button" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>
                        <div class="col-md-8">
                            {% if product.is_in_stock %}
                                <button class="btn btn-primary btn-lg me-2" onclick="addToCart({{ product.id }}, getQuantity())">
                                    <i class="fas fa-cart-plus"></i> Thêm vào giỏ hàng
                                </button>
                                <button class="btn btn-success btn-lg" onclick="buyNow({{ product.id }})">
                                    <i class="fas fa-bolt"></i> Mua ngay
                                </button>
                            {% else %}
                                <button class="btn btn-secondary btn-lg" disabled>
                                    <i class="fas fa-times"></i> Hết hàng
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Promotions -->
                <div class="promotions mb-4">
                    <h6>Khuyến mãi:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-gift text-danger"></i> Tặng tai nghe trị giá 500.000đ</li>
                        <li><i class="fas fa-shipping-fast text-success"></i> Miễn phí vận chuyển toàn quốc</li>
                        <li><i class="fas fa-shield-alt text-primary"></i> Bảo hành 24 tháng chính hãng</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Product Details Tabs -->
    <div class="product-details mt-5">
        <ul class="nav nav-tabs" id="productTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button">
                    Mô tả sản phẩm
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button">
                    Thông số kỹ thuật
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
                    Đánh giá (128)
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="productTabsContent">
            <!-- Description -->
            <div class="tab-pane fade show active" id="description" role="tabpanel">
                <div class="p-4">
                    {% if product.description %}
                        <p>{{ product.description }}</p>
                    {% else %}
                        <p>Thông tin mô tả sản phẩm sẽ được cập nhật sớm.</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Specifications -->
            <div class="tab-pane fade" id="specifications" role="tabpanel">
                <div class="p-4">
                    {% if product.specifications %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                {% for key, value in product.specifications.items() %}
                                <tr>
                                    <td class="fw-bold">{{ key.title() }}</td>
                                    <td>{{ value }}</td>
                                </tr>
                                {% endfor %}
                            </table>
                        </div>
                    {% else %}
                        <p>Thông số kỹ thuật sẽ được cập nhật sớm.</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Reviews -->
            <div class="tab-pane fade" id="reviews" role="tabpanel">
                <div class="p-4">
                    <div class="reviews-summary mb-4">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <div class="average-rating">
                                    <div class="rating-number h1 text-warning">4.8</div>
                                    <div class="stars text-warning mb-2">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="total-reviews text-muted">128 đánh giá</div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="rating-breakdown">
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="me-2">5 sao</span>
                                        <div class="progress flex-grow-1 me-2">
                                            <div class="progress-bar bg-warning" style="width: 70%"></div>
                                        </div>
                                        <span class="text-muted">70%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="me-2">4 sao</span>
                                        <div class="progress flex-grow-1 me-2">
                                            <div class="progress-bar bg-warning" style="width: 20%"></div>
                                        </div>
                                        <span class="text-muted">20%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="me-2">3 sao</span>
                                        <div class="progress flex-grow-1 me-2">
                                            <div class="progress-bar bg-warning" style="width: 7%"></div>
                                        </div>
                                        <span class="text-muted">7%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center mb-2">
                                        <span class="me-2">2 sao</span>
                                        <div class="progress flex-grow-1 me-2">
                                            <div class="progress-bar bg-warning" style="width: 2%"></div>
                                        </div>
                                        <span class="text-muted">2%</span>
                                    </div>
                                    <div class="rating-bar d-flex align-items-center">
                                        <span class="me-2">1 sao</span>
                                        <div class="progress flex-grow-1 me-2">
                                            <div class="progress-bar bg-warning" style="width: 1%"></div>
                                        </div>
                                        <span class="text-muted">1%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sample Reviews -->
                    <div class="reviews-list">
                        <div class="review-item border-bottom pb-3 mb-3">
                            <div class="reviewer-info d-flex align-items-center mb-2">
                                <div class="reviewer-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                    N
                                </div>
                                <div>
                                    <div class="reviewer-name fw-bold">Nguyễn Văn A</div>
                                    <div class="review-date text-muted small">15/01/2024</div>
                                </div>
                            </div>
                            <div class="review-rating mb-2">
                                <div class="stars text-warning">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            <div class="review-content">
                                <p>Sản phẩm rất tốt, chất lượng như mong đợi. Giao hàng nhanh, đóng gói cẩn thận.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products -->
    {% if related_products %}
    <div class="related-products mt-5">
        <h3 class="mb-4">Sản phẩm liên quan</h3>
        <div class="row">
            {% for related_product in related_products %}
            <div class="col-lg-3 col-md-4 col-6 mb-4">
                <div class="product-card card h-100 border-0 shadow-sm">
                    <div class="product-image">
                        <a href="/api/v1/product/{{ related_product.slug }}">
                            {% if related_product.main_image_url %}
                                <img src="{{ related_product.main_image_url }}" class="card-img-top" alt="{{ related_product.name }}" style="height: 200px; object-fit: cover;">
                            {% else %}
                                <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                        </a>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="/api/v1/product/{{ related_product.slug }}" class="text-decoration-none text-dark">
                                {{ related_product.name }}
                            </a>
                        </h6>
                        <div class="price-section">
                            <div class="current-price h6 text-danger">
                                {{ "{:,.0f}".format(related_product.price) }}₫
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function changeMainImage(imageUrl) {
    document.getElementById('mainImage').src = imageUrl;
}

function increaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    if (currentValue < 10) {
        quantityInput.value = currentValue + 1;
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
    }
}

function getQuantity() {
    return parseInt(document.getElementById('quantity').value);
}

function buyNow(productId) {
    // Add to cart first, then redirect to checkout
    addToCart(productId, getQuantity());
    // TODO: Redirect to checkout
    setTimeout(() => {
        window.location.href = '/api/v1/checkout';
    }, 1000);
}
</script>
{% endblock %}
