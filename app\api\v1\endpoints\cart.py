from typing import List
from fastapi import APIRouter, HTTPException, Depends
from app.schemas.cart import Cart, CartItem, CartItemCreate, CartItemUpdate, CartSummary
from app.api.deps import get_current_user
from app.models.product import FAKE_PRODUCTS

router = APIRouter()

# Fake cart database
FAKE_CARTS = {}  # user_email -> cart_data


def get_user_cart(user_email: str):
    """Get or create cart for user"""
    if user_email not in FAKE_CARTS:
        FAKE_CARTS[user_email] = {
            "id": len(FAKE_CARTS) + 1,
            "user_id": 1,  # Would be actual user ID
            "items": [],
            "total_items": 0,
            "total_amount": 0
        }
    return FAKE_CARTS[user_email]


def calculate_cart_totals(cart):
    """Calculate cart totals"""
    total_items = sum(item["quantity"] for item in cart["items"])
    total_amount = sum(item["subtotal"] for item in cart["items"])
    
    cart["total_items"] = total_items
    cart["total_amount"] = total_amount
    return cart


def get_product_by_id(product_id: int):
    """Get product by ID"""
    for product in FAKE_PRODUCTS:
        if product["id"] == product_id:
            return product
    return None


@router.get("/", response_model=Cart)
async def get_cart(current_user_email: str = Depends(get_current_user)):
    """
    Lấy giỏ hàng của user hiện tại
    """
    cart = get_user_cart(current_user_email)
    
    # Add product info to cart items
    for item in cart["items"]:
        product = get_product_by_id(item["product_id"])
        if product:
            item["product"] = {
                "id": product["id"],
                "name": product["name"],
                "slug": product["slug"],
                "price": product["price"],
                "main_image_url": product["main_image_url"],
                "is_in_stock": product["is_in_stock"]
            }
    
    return calculate_cart_totals(cart)


@router.post("/items", response_model=CartItem)
async def add_to_cart(
    item: CartItemCreate,
    current_user_email: str = Depends(get_current_user)
):
    """
    Thêm sản phẩm vào giỏ hàng
    """
    # Check if product exists
    product = get_product_by_id(item.product_id)
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    if not product["is_in_stock"]:
        raise HTTPException(status_code=400, detail="Product is out of stock")
    
    cart = get_user_cart(current_user_email)
    
    # Check if item already exists in cart
    existing_item = None
    for cart_item in cart["items"]:
        if cart_item["product_id"] == item.product_id:
            existing_item = cart_item
            break
    
    if existing_item:
        # Update quantity
        existing_item["quantity"] += item.quantity
        existing_item["subtotal"] = existing_item["quantity"] * product["price"]
        new_item = existing_item
    else:
        # Add new item
        new_item = {
            "id": len(cart["items"]) + 1,
            "cart_id": cart["id"],
            "product_id": item.product_id,
            "quantity": item.quantity,
            "subtotal": item.quantity * product["price"],
            "product": {
                "id": product["id"],
                "name": product["name"],
                "slug": product["slug"],
                "price": product["price"],
                "main_image_url": product["main_image_url"],
                "is_in_stock": product["is_in_stock"]
            }
        }
        cart["items"].append(new_item)
    
    calculate_cart_totals(cart)
    return new_item


@router.put("/items/{item_id}", response_model=CartItem)
async def update_cart_item(
    item_id: int,
    item_update: CartItemUpdate,
    current_user_email: str = Depends(get_current_user)
):
    """
    Cập nhật số lượng sản phẩm trong giỏ hàng
    """
    cart = get_user_cart(current_user_email)
    
    for cart_item in cart["items"]:
        if cart_item["id"] == item_id:
            product = get_product_by_id(cart_item["product_id"])
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")
            
            cart_item["quantity"] = item_update.quantity
            cart_item["subtotal"] = cart_item["quantity"] * product["price"]
            
            calculate_cart_totals(cart)
            return cart_item
    
    raise HTTPException(status_code=404, detail="Cart item not found")


@router.delete("/items/{item_id}")
async def remove_from_cart(
    item_id: int,
    current_user_email: str = Depends(get_current_user)
):
    """
    Xóa sản phẩm khỏi giỏ hàng
    """
    cart = get_user_cart(current_user_email)
    
    for i, cart_item in enumerate(cart["items"]):
        if cart_item["id"] == item_id:
            removed_item = cart["items"].pop(i)
            calculate_cart_totals(cart)
            return {"message": "Item removed from cart"}
    
    raise HTTPException(status_code=404, detail="Cart item not found")


@router.delete("/")
async def clear_cart(current_user_email: str = Depends(get_current_user)):
    """
    Xóa tất cả sản phẩm trong giỏ hàng
    """
    cart = get_user_cart(current_user_email)
    cart["items"] = []
    calculate_cart_totals(cart)
    return {"message": "Cart cleared"}


@router.get("/summary", response_model=CartSummary)
async def get_cart_summary(current_user_email: str = Depends(get_current_user)):
    """
    Lấy tóm tắt giỏ hàng (số lượng và tổng tiền)
    """
    cart = get_user_cart(current_user_email)
    calculate_cart_totals(cart)
    
    return {
        "total_items": cart["total_items"],
        "total_amount": cart["total_amount"]
    }
