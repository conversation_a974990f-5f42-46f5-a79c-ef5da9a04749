from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from app.schemas.product import Product, ProductCreate, ProductUpdate, ProductList, ProductSearch
from app.models.product import FAKE_PRODUCTS
from app.models.category import FAKE_CATEGORIES
from app.models.brand import FAKE_BRANDS

router = APIRouter()


def get_category_by_id(category_id: int):
    """Helper function to get category by ID"""
    for category in FAKE_CATEGORIES:
        if category["id"] == category_id:
            return category
    return None


def get_brand_by_id(brand_id: int):
    """Helper function to get brand by ID"""
    for brand in FAKE_BRANDS:
        if brand["id"] == brand_id:
            return brand
    return None


@router.get("/", response_model=List[ProductList])
async def get_products(
    q: Optional[str] = Query(None, description="Search query"),
    category_id: Optional[int] = Query(None, description="Filter by category"),
    brand_id: Optional[int] = Query(None, description="Filter by brand"),
    min_price: Optional[float] = Query(None, description="Minimum price"),
    max_price: Optional[float] = Query(None, description="Maximum price"),
    is_featured: Optional[bool] = Query(None, description="Featured products only"),
    is_in_stock: Optional[bool] = Query(True, description="In stock products only"),
    sort_by: str = Query("created_at", description="Sort by: price_asc, price_desc, name, created_at"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page")
):
    """
    Lấy danh sách sản phẩm với tìm kiếm và lọc
    """
    filtered_products = FAKE_PRODUCTS.copy()
    
    # Apply filters
    if q:
        filtered_products = [
            p for p in filtered_products 
            if q.lower() in p["name"].lower() or q.lower() in p["description"].lower()
        ]
    
    if category_id:
        filtered_products = [p for p in filtered_products if p["category_id"] == category_id]
    
    if brand_id:
        filtered_products = [p for p in filtered_products if p["brand_id"] == brand_id]
    
    if min_price:
        filtered_products = [p for p in filtered_products if p["price"] >= min_price]
    
    if max_price:
        filtered_products = [p for p in filtered_products if p["price"] <= max_price]
    
    if is_featured is not None:
        filtered_products = [p for p in filtered_products if p["is_featured"] == is_featured]
    
    if is_in_stock is not None:
        filtered_products = [p for p in filtered_products if p["is_in_stock"] == is_in_stock]
    
    # Apply sorting
    if sort_by == "price_asc":
        filtered_products.sort(key=lambda x: x["price"])
    elif sort_by == "price_desc":
        filtered_products.sort(key=lambda x: x["price"], reverse=True)
    elif sort_by == "name":
        filtered_products.sort(key=lambda x: x["name"])
    
    # Apply pagination
    start = (page - 1) * limit
    end = start + limit
    
    return filtered_products[start:end]


@router.get("/featured", response_model=List[ProductList])
async def get_featured_products(limit: int = Query(10, ge=1, le=50)):
    """
    Lấy danh sách sản phẩm nổi bật
    """
    featured_products = [p for p in FAKE_PRODUCTS if p.get("is_featured", False)]
    return featured_products[:limit]


@router.get("/{product_id}", response_model=Product)
async def get_product(product_id: int):
    """
    Lấy thông tin chi tiết sản phẩm
    """
    for product in FAKE_PRODUCTS:
        if product["id"] == product_id:
            # Add category and brand info
            product_with_relations = product.copy()
            product_with_relations["category"] = get_category_by_id(product["category_id"])
            product_with_relations["brand"] = get_brand_by_id(product["brand_id"])
            return product_with_relations
    raise HTTPException(status_code=404, detail="Product not found")


@router.get("/slug/{slug}", response_model=Product)
async def get_product_by_slug(slug: str):
    """
    Lấy sản phẩm theo slug
    """
    for product in FAKE_PRODUCTS:
        if product["slug"] == slug:
            product_with_relations = product.copy()
            product_with_relations["category"] = get_category_by_id(product["category_id"])
            product_with_relations["brand"] = get_brand_by_id(product["brand_id"])
            return product_with_relations
    raise HTTPException(status_code=404, detail="Product not found")


@router.post("/", response_model=Product)
async def create_product(product: ProductCreate):
    """
    Tạo sản phẩm mới
    """
    # Validate category and brand exist
    if not get_category_by_id(product.category_id):
        raise HTTPException(status_code=400, detail="Category not found")

    if not get_brand_by_id(product.brand_id):
        raise HTTPException(status_code=400, detail="Brand not found")

    new_id = max([p["id"] for p in FAKE_PRODUCTS]) + 1 if FAKE_PRODUCTS else 1
    new_product = {
        "id": new_id,
        **product.dict()
    }
    FAKE_PRODUCTS.append(new_product)

    # Return with relations
    new_product["category"] = get_category_by_id(product.category_id)
    new_product["brand"] = get_brand_by_id(product.brand_id)
    return new_product


@router.put("/{product_id}", response_model=Product)
async def update_product(product_id: int, product: ProductUpdate):
    """
    Cập nhật thông tin sản phẩm
    """
    for i, existing_product in enumerate(FAKE_PRODUCTS):
        if existing_product["id"] == product_id:
            updated_product = existing_product.copy()
            update_data = product.dict(exclude_unset=True)

            # Validate category and brand if provided
            if "category_id" in update_data and not get_category_by_id(update_data["category_id"]):
                raise HTTPException(status_code=400, detail="Category not found")

            if "brand_id" in update_data and not get_brand_by_id(update_data["brand_id"]):
                raise HTTPException(status_code=400, detail="Brand not found")

            updated_product.update(update_data)
            FAKE_PRODUCTS[i] = updated_product

            # Return with relations
            updated_product["category"] = get_category_by_id(updated_product["category_id"])
            updated_product["brand"] = get_brand_by_id(updated_product["brand_id"])
            return updated_product
    raise HTTPException(status_code=404, detail="Product not found")


@router.delete("/{product_id}")
async def delete_product(product_id: int):
    """
    Xóa sản phẩm
    """
    for i, product in enumerate(FAKE_PRODUCTS):
        if product["id"] == product_id:
            deleted_product = FAKE_PRODUCTS.pop(i)
            return {"message": f"Product {deleted_product['name']} deleted successfully"}
    raise HTTPException(status_code=404, detail="Product not found")


@router.get("/category/{category_id}", response_model=List[ProductList])
async def get_products_by_category(
    category_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100)
):
    """
    Lấy sản phẩm theo danh mục
    """
    if not get_category_by_id(category_id):
        raise HTTPException(status_code=404, detail="Category not found")

    category_products = [p for p in FAKE_PRODUCTS if p["category_id"] == category_id]

    start = (page - 1) * limit
    end = start + limit

    return category_products[start:end]


@router.get("/brand/{brand_id}", response_model=List[ProductList])
async def get_products_by_brand(
    brand_id: int,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100)
):
    """
    Lấy sản phẩm theo thương hiệu
    """
    if not get_brand_by_id(brand_id):
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_products = [p for p in FAKE_PRODUCTS if p["brand_id"] == brand_id]

    start = (page - 1) * limit
    end = start + limit

    return brand_products[start:end]
