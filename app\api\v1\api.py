from fastapi import APIRouter

from app.api.v1.endpoints import (
    categories,
    brands, 
    products,
    users,
    auth,
    cart,
    orders,
    reviews,
    home
)

api_router = APIRouter()

# Public endpoints
api_router.include_router(home.router, tags=["home"])
api_router.include_router(categories.router, prefix="/categories", tags=["categories"])
api_router.include_router(brands.router, prefix="/brands", tags=["brands"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# User endpoints
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(cart.router, prefix="/cart", tags=["cart"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
