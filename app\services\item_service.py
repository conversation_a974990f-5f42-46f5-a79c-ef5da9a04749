from typing import List, Optional
from app.schemas.item import Item, ItemCreate, ItemUpdate


class ItemService:
    """
    Service layer cho Item business logic
    """
    
    def __init__(self):
        # Fake database - sẽ được thay thế bằng database thực
        self.fake_db = [
            {"id": 1, "item_name": "Foo", "description": "A very nice Item"},
            {"id": 2, "item_name": "Bar", "description": "The bartenders"},
            {"id": 3, "item_name": "<PERSON><PERSON>", "description": "There goes my hero"}
        ]
    
    def get_items(self, skip: int = 0, limit: int = 10) -> List[dict]:
        """Lấy danh sách items với phân trang"""
        return self.fake_db[skip: skip + limit]
    
    def get_item_by_id(self, item_id: int) -> Optional[dict]:
        """Lấy item theo ID"""
        for item in self.fake_db:
            if item["id"] == item_id:
                return item
        return None
    
    def create_item(self, item_data: ItemCreate) -> dict:
        """Tạo item mới"""
        new_id = max([item["id"] for item in self.fake_db]) + 1 if self.fake_db else 1
        new_item = {
            "id": new_id,
            "item_name": item_data.item_name,
            "description": item_data.description
        }
        self.fake_db.append(new_item)
        return new_item
    
    def update_item(self, item_id: int, item_data: ItemUpdate) -> Optional[dict]:
        """Cập nhật item"""
        for i, item in enumerate(self.fake_db):
            if item["id"] == item_id:
                updated_item = item.copy()
                if item_data.item_name is not None:
                    updated_item["item_name"] = item_data.item_name
                if item_data.description is not None:
                    updated_item["description"] = item_data.description
                self.fake_db[i] = updated_item
                return updated_item
        return None
    
    def delete_item(self, item_id: int) -> Optional[dict]:
        """Xóa item"""
        for i, item in enumerate(self.fake_db):
            if item["id"] == item_id:
                return self.fake_db.pop(i)
        return None


# Singleton instance
item_service = ItemService()
