from typing import List
from fastapi import APIRouter, HTTPException, Depends
from app.schemas.order import Order, OrderCreate, OrderList, OrderStatus, PaymentMethod
from app.api.deps import get_current_user

router = APIRouter()

# Fake orders database
FAKE_ORDERS = []


@router.get("/", response_model=List[OrderList])
async def get_user_orders(current_user_email: str = Depends(get_current_user)):
    """
    L<PERSON>y danh sách đơn hàng của user
    """
    user_orders = [order for order in FAKE_ORDERS if order.get("user_email") == current_user_email]
    return user_orders


@router.get("/{order_id}", response_model=Order)
async def get_order(order_id: int, current_user_email: str = Depends(get_current_user)):
    """
    <PERSON><PERSON>y chi tiết đơn hàng
    """
    for order in FAKE_ORDERS:
        if order["id"] == order_id and order.get("user_email") == current_user_email:
            return order
    raise HTTPException(status_code=404, detail="Order not found")


@router.post("/", response_model=Order)
async def create_order(order: OrderCreate, current_user_email: str = Depends(get_current_user)):
    """
    Tạo đơn hàng mới từ giỏ hàng
    """
    # This would normally:
    # 1. Get user's cart
    # 2. Validate cart items
    # 3. Calculate totals
    # 4. Create order
    # 5. Clear cart
    
    new_order = {
        "id": len(FAKE_ORDERS) + 1,
        "order_number": f"ORD{len(FAKE_ORDERS) + 1:06d}",
        "user_email": current_user_email,
        "status": OrderStatus.PENDING,
        "payment_status": "pending",
        "payment_method": order.payment_method,
        "subtotal": 0,
        "shipping_fee": 30000,  # 30k VND
        "tax_amount": 0,
        "discount_amount": 0,
        "total_amount": 30000,
        "items": [],
        "notes": order.notes,
        "created_at": "2024-01-01T00:00:00"
    }
    
    FAKE_ORDERS.append(new_order)
    return new_order
