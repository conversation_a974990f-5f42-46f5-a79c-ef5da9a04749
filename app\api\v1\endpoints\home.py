from fastapi import APIRouter, Request
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

router = APIRouter()
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def root():
    return {"message": "xin chao! Tao la Truong haaa"}


@router.post("/")
async def post():
    return {
        "tin nhan": "chim cat goi dai bang",
        "ho ten": "Hoang manh long",
        "tuoi": "24"
    }


@router.put("/")
async def put():
    return {
        "dulieu01": "tao la 01",
        "dulieu02": "tao la 02",
        "dulieu03": "tao la 03"
    }


@router.get("/home", response_class=HTMLResponse)
async def home_page(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})
