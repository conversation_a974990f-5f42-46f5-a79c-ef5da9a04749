from typing import List
from fastapi import APIRouter, Request
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from app.schemas.product import ProductList
from app.schemas.category import Category
from app.schemas.brand import Brand
from app.models.product import FAKE_PRODUCTS
from app.models.category import FAKE_CATEGORIES
from app.models.brand import FAKE_BRANDS

router = APIRouter()
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def home_page(request: Request):
    """
    Trang chủ e-commerce
    """
    # Get featured products
    featured_products = [p for p in FAKE_PRODUCTS if p.get("is_featured", False)][:8]
    
    # Get categories
    categories = FAKE_CATEGORIES[:6]
    
    # Get popular brands
    brands = FAKE_BRANDS[:8]
    
    return templates.TemplateResponse("home.html", {
        "request": request,
        "featured_products": featured_products,
        "categories": categories,
        "brands": brands,
        "page_title": "TheGioiDiDong Clone - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> ki<PERSON> ch<PERSON> hãng"
    })


@router.get("/api/featured-products", response_model=List[ProductList])
async def get_featured_products_api():
    """
    API lấy sản phẩm nổi bật cho trang chủ
    """
    return [p for p in FAKE_PRODUCTS if p.get("is_featured", False)][:8]


@router.get("/api/categories", response_model=List[Category])
async def get_categories_api():
    """
    API lấy danh mục cho trang chủ
    """
    return FAKE_CATEGORIES


@router.get("/api/brands", response_model=List[Brand])
async def get_brands_api():
    """
    API lấy thương hiệu cho trang chủ
    """
    return FAKE_BRANDS


@router.get("/search", response_class=HTMLResponse)
async def search_page(request: Request, q: str = ""):
    """
    Trang tìm kiếm sản phẩm
    """
    search_results = []
    if q:
        search_results = [
            p for p in FAKE_PRODUCTS 
            if q.lower() in p["name"].lower() or q.lower() in p["description"].lower()
        ]
    
    return templates.TemplateResponse("search.html", {
        "request": request,
        "query": q,
        "products": search_results,
        "total_results": len(search_results),
        "page_title": f"Tìm kiếm: {q}" if q else "Tìm kiếm sản phẩm"
    })


@router.get("/category/{slug}", response_class=HTMLResponse)
async def category_page(request: Request, slug: str):
    """
    Trang danh mục sản phẩm
    """
    # Find category by slug
    category = None
    for cat in FAKE_CATEGORIES:
        if cat["slug"] == slug:
            category = cat
            break
    
    if not category:
        return templates.TemplateResponse("404.html", {
            "request": request,
            "message": "Danh mục không tồn tại"
        }, status_code=404)
    
    # Get products in this category
    category_products = [p for p in FAKE_PRODUCTS if p["category_id"] == category["id"]]
    
    return templates.TemplateResponse("category.html", {
        "request": request,
        "category": category,
        "products": category_products,
        "page_title": f"{category['name']} - TheGioiDiDong Clone"
    })


@router.get("/product/{slug}", response_class=HTMLResponse)
async def product_detail_page(request: Request, slug: str):
    """
    Trang chi tiết sản phẩm
    """
    # Find product by slug
    product = None
    for p in FAKE_PRODUCTS:
        if p["slug"] == slug:
            product = p
            break
    
    if not product:
        return templates.TemplateResponse("404.html", {
            "request": request,
            "message": "Sản phẩm không tồn tại"
        }, status_code=404)
    
    # Get category and brand info
    category = next((c for c in FAKE_CATEGORIES if c["id"] == product["category_id"]), None)
    brand = next((b for b in FAKE_BRANDS if b["id"] == product["brand_id"]), None)
    
    # Get related products (same category)
    related_products = [
        p for p in FAKE_PRODUCTS 
        if p["category_id"] == product["category_id"] and p["id"] != product["id"]
    ][:4]
    
    return templates.TemplateResponse("product_detail.html", {
        "request": request,
        "product": product,
        "category": category,
        "brand": brand,
        "related_products": related_products,
        "page_title": f"{product['name']} - TheGioiDiDong Clone"
    })
