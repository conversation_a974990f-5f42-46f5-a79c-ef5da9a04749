import pytest
from fastapi.testclient import TestClient
from app.main import app


@pytest.fixture
def client():
    """
    Test client fixture
    """
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """
    Authentication headers for testing protected endpoints
    """
    # This would normally create a test user and return auth headers
    # For now, we'll use a mock token
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_product():
    """
    Sample product data for testing
    """
    return {
        "name": "Test Product",
        "slug": "test-product",
        "description": "This is a test product",
        "short_description": "Test product",
        "sku": "TEST001",
        "price": 1000000,
        "original_price": 1200000,
        "discount_percentage": 16.7,
        "stock_quantity": 10,
        "is_in_stock": True,
        "category_id": 1,
        "brand_id": 1,
        "is_active": True,
        "is_featured": False
    }


@pytest.fixture
def sample_user():
    """
    Sample user data for testing
    """
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "phone": "0123456789"
    }
