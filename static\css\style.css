/* Custom CSS for TheGioiDiDong Clone */

/* Global Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Header Styles */
.header {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.top-bar {
    background-color: #f8f9fa;
    font-size: 0.875rem;
}

.logo h2 {
    font-weight: bold;
}

.search-form .form-control {
    border-radius: 25px 0 0 25px;
    border-right: none;
}

.search-form .btn {
    border-radius: 0 25px 25px 0;
}

/* Navigation */
.navbar {
    border-top: 1px solid #dee2e6;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* <PERSON> Banner */
.hero-banner {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Category Cards */
.category-card {
    transition: all 0.3s ease;
    background: white;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.category-icon {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.product-image {
    overflow: hidden;
}

.product-image img {
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-actions {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.price-section .current-price {
    font-weight: bold;
}

.price-section .original-price {
    font-size: 0.875rem;
}

/* Brand Cards */
.brand-card {
    transition: all 0.3s ease;
    background: white;
}

.brand-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Promotion Cards */
.promo-card {
    transition: all 0.3s ease;
}

.promo-card:hover {
    transform: translateY(-3px);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

/* Utility Classes */
.hover-shadow {
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Footer */
.footer {
    background-color: #2c3e50 !important;
}

.footer a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

.social-links a {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-banner h1 {
        font-size: 2rem;
    }
    
    .category-card {
        margin-bottom: 1rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
    
    .search-form {
        margin-bottom: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* Rating Stars */
.stars {
    font-size: 0.875rem;
}

.stars .fas {
    color: #ffc107;
}

.stars .far {
    color: #dee2e6;
}

/* Filters */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.filter-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

/* Modal Customizations */
.modal-header {
    background-color: var(--primary-color);
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}
